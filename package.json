{"name": "digital-commonwealth-dao", "version": "1.0.0", "description": "Digital Commonwealth DAO Backend API", "type": "module", "main": "app.js", "scripts": {"start": "node app.js", "start2": "node app2.js", "dev": "node --watch app.js", "dev2": "node --watch app2.js", "init-dao": "node initialize-dao.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["dao", "governance", "blockchain", "cryptocurrency"], "author": "Digital Commonwealth Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "body-parser": "^1.20.2", "mongoose": "^7.5.0", "jsonwebtoken": "^9.0.1", "tweetnacl": "^1.0.3", "bs58": "^5.0.0", "bip39": "^3.1.0", "@scure/bip32": "^1.3.2", "dotenv": "^16.3.1", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.1"}}
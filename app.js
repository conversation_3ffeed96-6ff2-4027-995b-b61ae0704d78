import express from 'express';
import bodyParser from 'body-parser';
import mongoose from 'mongoose';
import jwt from 'jsonwebtoken';
import crypto from 'crypto'; // For hashing, particularly for proposalHash
import { KeyGen } from './KeyGen.js'; // Ensure KeyGen.js is in the same directory or correctly path-adjusted
import User from './models/User.js'; // Your User model
import DAOConfig from './models/DAOConfig.js'; // Your DAOConfig model
import GovernanceProposal from './models/GovernanceProposal.js'; // Your GovernanceProposal model

const app = express();
app.use(bodyParser.json());

// --- Mongoose Connection ---
// Replace with your actual MongoDB URI (e.g., 'mongodb://localhost:27017/digital_commonwealth')
mongoose.connect('mongodb://localhost:27017/digital_commonwealth')
    .then(() => console.log('MongoDB connected successfully!'))
    .catch(err => {
        console.error('MongoDB connection error:', err);
        // It's generally good practice to exit if database connection is critical,
        // but for development/prototyping, continuing might be acceptable.
        // For now, we'll just log and continue as per your earlier modification intent.
        console.log('Continuing without MongoDB connection...');
    });

// --- JWT Secret (for session tokens) ---
// IMPORTANT: In production, load this from environment variables (e.g., process.env.JWT_SECRET)
const JWT_SECRET = 'your_super_secret_jwt_key_for_digital_commonwealth'; 

// --- Global DAO Config Cache & Loader ---
// We'll load the DAOConfig once on startup and keep it in memory for quick access.
// This simulates fetching immutable blockchain state.
let globalDaoConfig = null;

const loadDAOConfig = async () => {
    try {
        globalDaoConfig = await DAOConfig.findById('global_dao_config');
        if (globalDaoConfig) {
            console.log('DAOConfig loaded successfully.');
        } else {
            console.warn('DAOConfig document not found. It needs to be initialized via POST /api/dao-config/initialize.');
        }
    } catch (error) {
        console.error("Failed to load DAOConfig on startup:", error);
    }
};
loadDAOConfig(); // Call once on application start

// --- Middleware for authenticating requests ---
// Extracts publicKey from JWT token, attaches to req.user
const authenticateToken = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) return res.status(401).json({ message: "No authentication token provided." });

    jwt.verify(token, JWT_SECRET, (err, userPayload) => {
        if (err) return res.status(403).json({ message: "Invalid or expired authentication token." });
        // userPayload should contain { publicKey, username }
        req.user = userPayload; 
        next();
    });
};

// --- Middleware to check if the authenticated user is the DAO owner ---
// Requires authenticateToken to run before it.
const authorizeDAOOwner = async (req, res, next) => {
    if (!globalDaoConfig) {
        return res.status(503).json({ message: "DAO configuration not initialized. Please try again later." });
    }
    if (req.user.publicKey !== globalDaoConfig.ownerPublicKey) {
        return res.status(403).json({ message: "Unauthorized. Only the DAO owner can perform this action." });
    }
    next();
};

// --- Middleware to check if user holds minimum tokens (simulates 'onlyTokenHolder') ---
// This middleware explicitly uses the User model to check reputationScore.
const checkMinTokens = async (req, res, next) => {
    try {
        const currentUser = await User.findById(req.user.publicKey); // Explicitly using User model here
        if (!currentUser) {
            return res.status(404).json({ message: "Authenticated user not found." });
        }
        const daoConfig = globalDaoConfig; // Use the cached globalDaoConfig
        if (!daoConfig) {
             console.error('DAOConfig not loaded in cache. This should not happen if initialization is successful.');
             return res.status(503).json({ message: "Server configuration error: DAOConfig not available." });
        }
        if (currentUser.reputationScore < daoConfig.minProposalCreationTokens) {
            return res.status(403).json({ message: `Insufficient tokens. Requires at least ${daoConfig.minProposalCreationTokens} ${daoConfig.nativeCoinSymbol}.` });
        }
        next();
    } catch (error) {
        console.error("Error checking token balance in checkMinTokens middleware:", error);
        res.status(500).json({ message: "Internal server error during token balance check." });
    }
};

// --- API ROUTES FOR DAOConfig ---

// POST /api/dao-config/initialize
// Initializes the global DAO configuration. Can only be called once.
// Requires: ownerPublicKey (the initial administrator's pubkey), and a signature from that owner
// over the entire configuration payload to prove original intent.
app.post('/api/dao-config/initialize', async (req, res) => {
    // Prevent re-initialization if config already exists
    if (globalDaoConfig) {
        return res.status(409).json({ message: "DAO configuration already initialized." });
    }

    const {
        ownerPublicKey,
        nativeCoinName,
        nativeCoinSymbol,
        totalSupply,
        initialDistributionStrategy,
        genesisAllocations,
        votingDuration,
        minimumQuorumPercentage,
        minProposalCreationTokens,
        // clientProvidedSignature and messageSignedByOwner are crucial for verification
        clientProvidedSignature,
        messageSignedByOwner // The stringified payload that the owner signed
    } = req.body;

    // Basic validation
    if (!ownerPublicKey || !clientProvidedSignature || !messageSignedByOwner || !nativeCoinName || !nativeCoinSymbol || totalSupply == null || votingDuration == null || minimumQuorumPercentage == null || minProposalCreationTokens == null) {
        return res.status(400).json({ message: "Missing required DAO configuration parameters or signature details." });
    }

    try {
        // 1. Verify the owner's signature over the entire payload
        // This is a critical step: the owner must sign the exact configuration they intend to initialize.
        const isSignatureValid = KeyGen.verifySignature(messageSignedByOwner, clientProvidedSignature, ownerPublicKey);
        if (!isSignatureValid) {
            return res.status(401).json({ message: "Invalid owner signature for initialization." });
        }

        const newConfig = new DAOConfig({
            _id: 'global_dao_config', // Enforces single document
            ownerPublicKey,
            nativeCoinName,
            nativeCoinSymbol,
            totalSupply,
            initialDistributionStrategy,
            genesisAllocations: genesisAllocations || [], // Ensure it's an array
            votingDuration,
            minimumQuorumPercentage,
            minProposalCreationTokens
        });

        const savedConfig = await newConfig.save();
        globalDaoConfig = savedConfig; // Update in-memory cache

        // After successful initialization, you might want to distribute initial tokens
        // For simplicity, this is not implemented here but would involve updating User.reputationScore
        // based on genesisAllocations or a separate admin 'mint' function.
        console.log(`Event: DAOConfigInitialized - Owner: ${ownerPublicKey}, Total Supply: ${totalSupply}`);

        res.status(201).json({ message: "DAO configuration initialized successfully.", config: savedConfig });

    } catch (error) {
        // Handle cases where _id 'global_dao_config' already exists (due to unique:true)
        if (error.code === 11000) { // MongoDB duplicate key error
            return res.status(409).json({ message: "DAO configuration has already been initialized." });
        }
        console.error("Error initializing DAO config:", error);
        res.status(500).json({ message: "Server error during DAO configuration initialization." });
    }
});

// GET /api/dao-config
// Retrieves the current global DAO configuration. Publicly accessible.
app.get('/api/dao-config', (req, res) => {
    if (!globalDaoConfig) {
        return res.status(404).json({ message: "DAO configuration not found. It needs to be initialized." });
    }
    res.json({ config: globalDaoConfig });
});

// PUT /api/dao-config/update
// Updates the global DAO configuration. Only accessible by the ownerPublicKey
// and requires a signed message from the owner.
app.put('/api/dao-config/update', authenticateToken, authorizeDAOOwner, async (req, res) => {
    if (!globalDaoConfig) {
        return res.status(404).json({ message: "DAO configuration not found. It needs to be initialized." });
    }

    const {
        // Parameters allowed to be updated (e.g., votingDuration, minProposalCreationTokens, quorum)
        // ownerPublicKey should NOT be updated via this route, if ever, it requires a separate governance proposal.
        votingDuration,
        minimumQuorumPercentage,
        minProposalCreationTokens,
        // Add other updatable fields as needed.
        // And the signature for the update payload
        clientProvidedSignature,
        messageSignedByOwner // The stringified payload that the owner signed
    } = req.body;

    if (!clientProvidedSignature || !messageSignedByOwner) {
        return res.status(400).json({ message: "Missing owner signature for update." });
    }

    try {
        // Construct the message that was signed by the client for verification
        // This should be the exact subset of fields being updated, stringified.
        const updatePayloadToVerify = JSON.stringify({
            votingDuration,
            minimumQuorumPercentage,
            minProposalCreationTokens,
            // ... include any other fields that are being updated
        });

        const isSignatureValid = KeyGen.verifySignature(updatePayloadToVerify, clientProvidedSignature, req.user.publicKey);
        if (!isSignatureValid) {
            return res.status(401).json({ message: "Invalid owner signature for update." });
        }

        // Apply updates
        if (votingDuration != null) globalDaoConfig.votingDuration = votingDuration;
        if (minimumQuorumPercentage != null) globalDaoConfig.minimumQuorumPercentage = minimumQuorumPercentage;
        if (minProposalCreationTokens != null) globalDaoConfig.minProposalCreationTokens = minProposalCreationTokens;
        // ... apply other updates similarly

        const updatedConfig = await globalDaoConfig.save();
        // globalDaoConfig is already updated by reference due to Mongoose save

        console.log(`Event: DAOConfigUpdated - By: ${req.user.publicKey}`);
        res.json({ message: "DAO configuration updated successfully.", config: updatedConfig });

    } catch (error) {
        console.error("Error updating DAO config:", error);
        res.status(500).json({ message: "Server error during DAO configuration update." });
    }
});


// --- Other API routes (Identity, Governance, EAC) would be defined here ---

// POST /api/governance/proposals (createProposal)
// Requires authentication and minimum token balance (now explicitly using User)
app.post('/api/governance/proposals', authenticateToken, checkMinTokens, async (req, res) => {
    const { title, description, clientProvidedAuthorSignature, messageSignedByAuthor, category, relatedEntityId } = req.body;
    const authorPublicKey = req.user.publicKey; // From authenticated token payload

    if (!title || !description || !clientProvidedAuthorSignature || !messageSignedByAuthor) {
        return res.status(400).json({ message: "Title, description, author signature, and signed message are required." });
    }

    try {
        // 1. Verify the author's signature over the proposal content
        // The messageSignedByAuthor should be the exact string that the client signed (e.g., title + description).
        const isAuthorSignatureValid = KeyGen.verifySignature(messageSignedByAuthor, clientProvidedAuthorSignature, authorPublicKey);
        if (!isAuthorSignatureValid) {
            return res.status(401).json({ message: "Invalid author signature. Proposal creation failed." });
        }

        // Calculate proposalHash on server side for consistency with schema pre-save hook
        const contentToHash = title + description;
        const msgUint8 = KeyGen.encodeUTF8(contentToHash);
        const proposalHash = crypto.createHash('sha256').update(msgUint8).digest('base64');

        // Check for duplicate proposal content (hash) to prevent spam
        const existingProposal = await GovernanceProposal.findOne({ proposalHash });
        if (existingProposal) {
            return res.status(409).json({ message: "A proposal with this exact content already exists." });
        }

        // Get DAO config for voting duration
        const daoConfig = globalDaoConfig;
        if (!daoConfig) {
             console.error('DAOConfig not loaded. Please initialize it.');
             return res.status(500).json({ message: "Server configuration error: DAOConfig not available." });
        }

        const now = Math.floor(Date.now() / 1000); // Current Unix timestamp in seconds
        const votingDurationSeconds = daoConfig.votingDuration;

        const newProposal = new GovernanceProposal({
            title,
            description,
            authorPublicKey,
            authorSignature: clientProvidedAuthorSignature, // Store the client-provided signature
            proposalHash,
            status: 'Active', // Proposals are active immediately after creation
            startTime: now,
            endTime: now + votingDurationSeconds,
            threshold: daoConfig.minimumQuorumPercentage, // Store quorum as percentage
            category,
            relatedEntityId
        });

        await newProposal.save();
        // Emit ProposalCreated event (log to console for now, future Kafka/RabbitMQ)
        console.log(`Event: ProposalCreated - ID: ${newProposal._id}, Proposer: ${authorPublicKey}, Title: "${title}"`);

        res.status(201).json({ message: "Proposal created successfully!", proposal: newProposal });
    } catch (error) {
        console.error("Error creating proposal:", error);
        res.status(500).json({ message: error.message || "Server error creating proposal." });
    }
});

// Placeholder for other routes (to be imported/defined, e.g., Identity, EAC)
app.get('/', (req, res) => {
    res.send('Digital Commonwealth Backend Running! Access APIs at /api/...');
});

// Start the server
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
    console.log(`Digital Commonwealth API server listening on port ${PORT}`);
    console.log(`Test endpoint available at http://localhost:${PORT}/test`);

    // --- Client-side example to initialize as founder (Moved here) ---
    // This function will attempt to initialize the DAO config once the server is ready.
    const initializeDAOAsFounder = async () => {
        try {
            console.log('Attempting to initialize DAO config as founder...');
            const myKeypair = await KeyGen.generateHDWallet();
            const founderPublicKey = myKeypair.publicKeyBase58Nacl;

            // --- Register Founder as a User first ---
            // This is a crucial step to ensure the founder's public key exists in the User model,
            // which is a prerequisite for JWT authentication in subsequent requests (like creating proposals).
            try {
                const userResponse = await fetch(`http://localhost:${PORT}/api/identity/register`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        username: 'FounderUser', // A default username for the founder
                        publicKey: founderPublicKey
                    })
                });
                const userData = await userResponse.json();
                if (userResponse.ok) {
                    console.log('Founder User registered successfully (or already exists):', userData.user.username);
                    // Optionally, if this is the very first run, mint initial tokens to the founder.
                    // This would ideally be a separate admin function or part of a genesis script.
                    // For now, we assume reputationScore starts at 0 and is increased by other means.
                } else if (userResponse.status === 409) {
                    console.log('Founder User already registered.');
                } else {
                    console.error('Founder User registration failed:', userData.message);
                    return; // Stop if registration fails
                }
            } catch (userRegError) {
                console.error('Error during Founder User registration:', userRegError);
                return; // Stop if registration fails
            }


            const configPayload = {
                ownerPublicKey: founderPublicKey,
                nativeCoinName: "Commonwealth Coin",
                nativeCoinSymbol: "CMC",
                totalSupply: 1000000000,
                initialDistributionStrategy: "Genesis_Allocation",
                genesisAllocations: [
                    { publicKey: founderPublicKey, amount: 100000000 } // 10% to founder
                ],
                votingDuration: 604800, // 7 days in seconds
                minimumQuorumPercentage: 0.25,
                minProposalCreationTokens: 10000
            };

            // Sign the payload using the founder's private key
            const messageToSign = JSON.stringify(configPayload);
            const signature = KeyGen.signMessage(messageToSign, myKeypair.privateKeyNacl);

            // Send the request to initialize the DAO config
            const response = await fetch(`http://localhost:${PORT}/api/dao-config/initialize`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    ...configPayload,
                    clientProvidedSignature: signature,
                    messageSignedByOwner: messageToSign
                })
            });

            const result = await response.json();
            if (response.ok) {
                console.log('DAO Initialization successful:', result);
            } else {
                console.error('DAO Initialization failed:', result.message);
            }
        } catch (error) {
            console.error('Error during DAO initialization example:', error);
        }
    };

    // Call the initialization example after the server starts.
    initializeDAOAsFounder();

    // Add a test endpoint
    app.get('/test', (req, res) => {
        res.json({ message: 'Server is running!' });
    });
});

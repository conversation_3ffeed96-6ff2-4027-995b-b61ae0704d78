import mongoose from 'mongoose';

const transactionSchema = new mongoose.Schema({
    // Unique transaction ID
    _id: {
        type: String,
        required: true,
        unique: true
    },
    
    // Transaction type
    type: {
        type: String,
        enum: ['genesis_allocation', 'admin_mint', 'admin_distribution', 'user_transfer', 'governance_reward', 'proposal_fee'],
        required: true
    },
    
    // Transaction participants
    from: {
        publicKey: {
            type: String,
            default: null // null for minting/genesis operations
        },
        username: {
            type: String,
            default: null
        }
    },
    
    to: {
        publicKey: {
            type: String,
            required: true
        },
        username: {
            type: String,
            required: true
        }
    },
    
    // Transaction details
    amount: {
        type: Number,
        required: true,
        min: 0
    },
    
    // Balances before and after transaction
    balances: {
        fromBefore: {
            type: Number,
            default: null
        },
        fromAfter: {
            type: Number,
            default: null
        },
        toBefore: {
            type: Number,
            required: true
        },
        toAfter: {
            type: Number,
            required: true
        }
    },
    
    // Transaction metadata
    memo: {
        type: String,
        maxlength: 500,
        default: ''
    },
    
    reason: {
        type: String,
        maxlength: 200,
        default: ''
    },
    
    // Cryptographic verification
    signature: {
        type: String,
        required: true
    },
    
    signedMessage: {
        type: String,
        required: true
    },
    
    // Transaction status
    status: {
        type: String,
        enum: ['pending', 'completed', 'failed', 'reversed'],
        default: 'completed'
    },
    
    // Block/batch information (for future blockchain integration)
    blockNumber: {
        type: Number,
        default: null
    },
    
    batchId: {
        type: String,
        default: null
    },
    
    // Gas/fee information (for future implementation)
    fee: {
        type: Number,
        default: 0,
        min: 0
    },
    
    // Related entities
    relatedProposalId: {
        type: String,
        default: null
    },
    
    relatedConfigId: {
        type: String,
        default: null
    },
    
    // Administrative information
    executedBy: {
        publicKey: {
            type: String,
            default: null
        },
        username: {
            type: String,
            default: null
        }
    },
    
    // Audit trail
    metadata: {
        type: mongoose.Schema.Types.Mixed,
        default: {}
    }
}, { 
    timestamps: true 
});

// Pre-save middleware to generate transaction ID
transactionSchema.pre('save', function(next) {
    if (this.isNew && !this._id) {
        // Generate a unique transaction ID with timestamp prefix for better sorting
        const timestamp = Date.now();
        const objectId = new mongoose.Types.ObjectId();
        this._id = `tx_${timestamp}_${objectId.toString()}`;
    }
    next();
});

// Static method to record a transaction
transactionSchema.statics.recordTransaction = async function(transactionData) {
    // Ensure _id is set if not provided
    if (!transactionData._id) {
        const timestamp = Date.now();
        const objectId = new mongoose.Types.ObjectId();
        transactionData._id = `tx_${timestamp}_${objectId.toString()}`;
    }

    const transaction = new this(transactionData);
    return await transaction.save();
};

// Static method to get transaction history for a user
transactionSchema.statics.getUserTransactions = async function(publicKey, options = {}) {
    const { limit = 50, offset = 0, type = null } = options;
    
    const query = {
        $or: [
            { 'from.publicKey': publicKey },
            { 'to.publicKey': publicKey }
        ]
    };
    
    if (type) {
        query.type = type;
    }
    
    const transactions = await this.find(query)
        .sort({ createdAt: -1 })
        .limit(parseInt(limit))
        .skip(parseInt(offset));
    
    const total = await this.countDocuments(query);
    
    return { transactions, total };
};

// Static method to get transaction statistics
transactionSchema.statics.getTransactionStats = async function() {
    const stats = await this.aggregate([
        {
            $group: {
                _id: '$type',
                count: { $sum: 1 },
                totalAmount: { $sum: '$amount' }
            }
        }
    ]);
    
    const totalTransactions = await this.countDocuments();
    const totalVolume = await this.aggregate([
        { $group: { _id: null, total: { $sum: '$amount' } } }
    ]);
    
    return {
        totalTransactions,
        totalVolume: totalVolume[0]?.total || 0,
        byType: stats
    };
};

// Instance method to reverse a transaction (for admin corrections)
transactionSchema.methods.reverse = async function(reason, executedBy) {
    if (this.status !== 'completed') {
        throw new Error('Can only reverse completed transactions');
    }
    
    // Create a reverse transaction
    const reverseTransaction = {
        type: 'admin_distribution', // or a new 'reversal' type
        from: this.to,
        to: this.from,
        amount: this.amount,
        balances: {
            fromBefore: this.balances.toAfter,
            fromAfter: this.balances.toBefore,
            toBefore: this.balances.fromAfter,
            toAfter: this.balances.fromBefore
        },
        memo: `Reversal of transaction ${this._id}`,
        reason: reason,
        signature: 'SYSTEM_REVERSAL',
        signedMessage: `REVERSAL:${this._id}:${reason}`,
        executedBy: executedBy,
        metadata: {
            originalTransactionId: this._id,
            reversalReason: reason
        }
    };
    
    // Mark this transaction as reversed
    this.status = 'reversed';
    this.metadata.reversalReason = reason;
    this.metadata.reversedAt = new Date();
    this.metadata.reversedBy = executedBy;
    
    await this.save();
    
    // Create and return the reverse transaction
    return await this.constructor.recordTransaction(reverseTransaction);
};

const Transaction = mongoose.model('Transaction', transactionSchema);

export default Transaction;

import mongoose from 'mongoose';
import { KeyGen } from './KeyGen'; // Assuming KeyGen class is in KeyGen.js
import crypto from 'crypto'; // Node.js built-in crypto for hashing

// --- Sub-schema for individual votes ---
const voteSchema = new mongoose.Schema({
    voterPublicKey: {
        type: String,
        required: true
    },
    voteType: {
        type: String,
        enum: ['for', 'against', 'abstain'],
        required: true
    },
    voteWeight: { // Actual token weight (reputationScore) at the time of voting
        type: Number,
        required: true,
        min: 0
    },
    voterSignature: { // Signature over the vote details by the voter
        type: String,
        required: true
    },
    votedAt: {
        type: Date,
        default: Date.now
    }
}, { _id: false });

// --- Main Governance Proposal Schema ---
const governanceProposalSchema = new mongoose.Schema({
    // Unique identifier for the proposal
    _id: {
        type: String,
        required: true,
        unique: true
    },

    title: {
        type: String,
        required: true,
        trim: true,
        maxlength: 200
    },
    description: {
        type: String,
        required: true,
        maxlength: 5000
    },

    // The public key (Base58) of the user who created this proposal
    authorPublicKey: {
        type: String,
        required: true
    },

    // Cryptographic hash of the proposal's core content (title + description)
    proposalHash: {
        type: String,
        required: true,
        unique: true
    },

    // Cryptographic signature by the author over the proposalHash
    authorSignature: {
        type: String,
        required: true
    },

    // Start time of the voting period (Unix timestamp in seconds)
    // Similar to Solana's BPF clock or Ethereum's block.timestamp
    startTime: {
        type: Number,
        required: true
    },
    // End time of the voting period (Unix timestamp in seconds)
    endTime: {
        type: Number,
        required: true
    },

    // Aggregated vote weights (sum of reputationScore)
    // These fields directly mirror your JSON's "votesFor" and "votesAgainst"
    totalWeightFor: {
        type: Number,
        default: 0,
        min: 0
    },
    totalWeightAgainst: {
        type: Number,
        default: 0,
        min: 0
    },

    // Current status of the proposal
    status: {
        type: String,
        enum: ['Draft', 'Active', 'VotingEnded', 'Passed', 'Failed', 'Executed'],
        default: 'Draft'
    },
    // Whether the proposed action has been executed
    executed: {
        type: Boolean,
        default: false
    },

    // Array of individual vote records (for auditing and detail)
    votes: [voteSchema],

    // Optional: Category for the proposal (e.g., 'EAC_Rule_Change', 'New_Feature')
    category: {
        type: String,
        trim: true,
        maxlength: 100
    },

    // Optional: Reference to a specific EAC rule or other entity
    relatedEntityId: {
        type: String,
        index: true
    }
}, { timestamps: true }); // Mongoose adds createdAt and updatedAt

// --- Pre-save middleware to generate _id and proposalHash ---
governanceProposalSchema.pre('save', async function(next) {
    if (this.isNew) {
        // Generate a UUID for the _id if not provided
        if (!this._id) {
            this._id = new mongoose.Types.ObjectId().toString(); // Use Mongoose's ObjectId for initial unique ID
        }

        // Generate the proposalHash from title and description
        const contentToHash = this.title + this.description;
        const msgUint8 = KeyGen.encodeUTF8(contentToHash);
        this.proposalHash = crypto.createHash('sha256').update(msgUint8).digest('base64');
    }
    next();
});

// --- Instance method to add a vote ---
// This method is called by the Governance API after receiving a signed vote from client.
// It includes server-side validation and signature verification.
governanceProposalSchema.methods.addVote = async function(voterPublicKey, voteType, voteWeight, clientProvidedSignature, messageToVerify) {
    // 1. Basic validation
    if (!['for', 'against', 'abstain'].includes(voteType)) {
        throw new Error('Invalid voteType. Must be "for", "against", or "abstain".');
    }
    if (voteWeight < 0) {
        throw new Error('Vote weight cannot be negative.');
    }

    // 2. Prevent duplicate votes from the same public key on this proposal
    const existingVoteIndex = this.votes.findIndex(vote => vote.voterPublicKey === voterPublicKey);
    if (existingVoteIndex !== -1) {
        // For a DAO feel, usually only one vote is allowed per address per proposal.
        throw new Error(`Voter with public key ${voterPublicKey} has already voted on this proposal.`);
    }

    // 3. Verify the signature (critical server-side validation using KeyGen)
    // The clientProvidedSignature is what the user generated locally using their private key.
    // The messageToVerify is the exact string/data the client signed.
    const isSignatureValid = KeyGen.verifySignature(messageToVerify, clientProvidedSignature, voterPublicKey);
    if (!isSignatureValid) {
        throw new Error('Invalid signature provided for the vote. Authentication failed.');
    }

    // 4. Add the validated vote to the votes array
    this.votes.push({
        voterPublicKey,
        voteType,
        voteWeight,
        voterSignature: clientProvidedSignature, // Store the provided signature
        votedAt: new Date()
    });

    // 5. Update aggregated vote counts
    if (voteType === 'for') {
        this.totalWeightFor += voteWeight;
    } else if (voteType === 'against') {
        this.totalWeightAgainst += voteWeight;
    }

    // Mark votes array and aggregated fields as modified so Mongoose saves them
    this.markModified('votes');
    this.markModified('totalWeightFor');
    this.markModified('totalWeightAgainst');

    // Return the updated proposal (optional, but useful for API response)
    return this.save();
};

const GovernanceProposal = mongoose.model('GovernanceProposal', governanceProposalSchema);
export default GovernanceProposal;

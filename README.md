# Digital Commonwealth DAO

A sophisticated Decentralized Autonomous Organization (DAO) backend built with Node.js, Express, and MongoDB. This project implements a complete governance system with cryptographic key management, proposal creation, voting mechanisms, and native token management.

## Features

### 🔐 Cryptographic Security
- **HD Wallet Generation**: BIP39 mnemonic phrases and BIP32 hierarchical deterministic key derivation
- **Ed25519 Signatures**: Modern elliptic curve cryptography for digital signatures
- **JWT Authentication**: Secure token-based authentication system

### 🏛️ DAO Governance
- **Proposal System**: Create, vote on, and execute governance proposals
- **Token-Based Voting**: Voting power based on reputation score (native tokens)
- **Configurable Parameters**: Voting duration, quorum requirements, proposal thresholds

### 👥 User Management
- **Identity System**: Public key-based user registration and authentication
- **Reputation Scoring**: Token balance system for governance participation
- **Profile Management**: User profiles with governance statistics

### 🪙 Native Token (CMC)
- **Commonwealth Coin**: Native governance token with configurable supply
- **Genesis Allocation**: Automatic initial token distribution to founders
- **Token Minting**: Admin functions for creating and distributing new tokens
- **User Transfers**: Peer-to-peer token transfers with cryptographic verification
- **Transaction Audit**: Complete transaction history and balance tracking
- **Reputation System**: Tokens represent voting power and proposal creation rights

## Project Structure

```
6ml/
├── models/                 # Database models
│   ├── User.js            # User identity and reputation model
│   ├── DAOConfig.js       # DAO configuration model
│   ├── GovernanceProposal.js # Proposal and voting model
│   └── Transaction.js     # Token transaction audit model
├── utils/                 # Utility functions
│   └── KeyGen.js          # Cryptographic key generation and management
├── app.js                 # Main Express application
├── initialize-dao.js      # DAO initialization script
├── package.json           # Dependencies and scripts
├── .env                   # Environment configuration
└── README.md              # This file
```

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd 6ml
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up MongoDB**
   - Install MongoDB locally or use MongoDB Atlas
   - Update the `MONGODB_URI` in `.env` if needed

4. **Configure environment variables**
   - Copy `.env` and update values as needed
   - Change `JWT_SECRET` for production use

## Usage

### Start the Server

```bash
# Development mode
npm run dev

# Production mode
npm start
```

The server will start on `http://localhost:3000` and automatically:
- Connect to MongoDB
- Initialize the DAO configuration
- Create a founder user account
- **Distribute genesis tokens** to the founder (100M CMC)
- Set up the governance system with full token functionality

### Initialize DAO Manually

```bash
npm run init-dao
```

## API Endpoints

### Identity Management
- `POST /api/identity/register` - Register a new user
- `POST /api/identity/login` - Authenticate and get JWT token
- `GET /api/identity/profile/:publicKey` - Get user profile
- `PUT /api/identity/profile` - Update user profile (authenticated)

### DAO Configuration
- `GET /api/dao-config` - Get current DAO configuration
- `POST /api/dao-config/initialize` - Initialize DAO (one-time)
- `PUT /api/dao-config/update` - Update DAO config (owner only)

### Governance
- `POST /api/governance/proposals` - Create a new proposal (authenticated, min tokens required)

### Token Management
- `GET /api/tokens/balance/:publicKey` - Get user's token balance
- `GET /api/tokens/supply` - Get token supply and distribution info
- `GET /api/tokens/transfers/:publicKey` - Get user's transaction history
- `GET /api/tokens/transactions/stats` - Get overall transaction statistics
- `POST /api/tokens/transfer` - Transfer tokens between users (authenticated)
- `POST /api/tokens/mint` - Mint new tokens (DAO owner only)
- `POST /api/tokens/distribute` - Distribute tokens from treasury (DAO owner only)

### Utility
- `GET /` - API status
- `GET /test` - Health check

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `PORT` | Server port | `3000` |
| `MONGODB_URI` | MongoDB connection string | `mongodb://localhost:27017/digital_commonwealth` |
| `JWT_SECRET` | JWT signing secret | `your_super_secret_jwt_key_for_digital_commonwealth` |
| `JWT_EXPIRES_IN` | JWT expiration time | `24h` |
| `CORS_ORIGIN` | Allowed CORS origins | `http://localhost:3000,http://localhost:3001` |

## Development

### Scripts
- `npm start` - Start the production server
- `npm run dev` - Start with file watching (requires Node.js 18+)
- `npm run init-dao` - Initialize DAO configuration
- `npm test` - Run tests (not implemented yet)

### Key Components

#### KeyGen Utility
Handles all cryptographic operations:
- Mnemonic generation (BIP39)
- HD wallet derivation (BIP32)
- Message signing and verification (Ed25519)
- Base58 encoding/decoding

#### User Model
- Public key as primary identifier
- Reputation score (token balance)
- Governance participation tracking
- Profile and metadata storage

#### DAO Configuration
- Single global configuration document
- Native token parameters
- Governance rules and thresholds
- Owner-only updates with signature verification

#### Governance Proposals
- Cryptographically signed proposals
- Vote tracking with signature verification
- Automatic status updates based on time and votes
- Audit trail for all governance actions

#### Token Management System
- **Genesis Allocation**: Automatic distribution of initial tokens during DAO setup
- **Admin Minting**: Secure token creation with owner signature verification
- **User Transfers**: Peer-to-peer transfers with cryptographic signatures
- **Transaction Audit**: Complete transaction history with balance tracking
- **Supply Monitoring**: Real-time token supply and distribution analytics

## Security Considerations

1. **Private Key Management**: Never store private keys on the server
2. **Signature Verification**: All critical operations require cryptographic signatures
3. **JWT Security**: Use strong secrets and appropriate expiration times
4. **Input Validation**: All user inputs are validated and sanitized
5. **Rate Limiting**: Consider implementing rate limiting for production

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see LICENSE file for details

## Support

For questions or issues, please open a GitHub issue or contact the development team.

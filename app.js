import express from 'express';
import bodyParser from 'body-parser';
import mongoose from 'mongoose';
import jwt from 'jsonwebtoken';
import crypto from 'crypto'; // For hashing, particularly for proposalHash
import cors from 'cors';
import dotenv from 'dotenv';
import { KeyGen } from './utils/KeyGen.js'; // Updated path to utils directory
import User from './models/User.js'; // Your User model
import DAOConfig from './models/DAOConfig.js'; // Your DAOConfig model
import GovernanceProposal from './models/GovernanceProposal.js'; // Your GovernanceProposal model
import Transaction from './models/Transaction.js'; // Transaction audit model

// Load environment variables
dotenv.config();

const app = express();

// Middleware setup
app.use(cors({
    origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000', 'http://localhost:3001'],
    credentials: true
}));
app.use(bodyParser.json());

// --- Mongoose Connection ---
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/digital_commonwealth';
mongoose.connect(MONGODB_URI)
    .then(() => console.log('MongoDB connected successfully!'))
    .catch(err => {
        console.error('MongoDB connection error:', err);
        // It's generally good practice to exit if database connection is critical,
        // but for development/prototyping, continuing might be acceptable.
        // For now, we'll just log and continue as per your earlier modification intent.
        console.log('Continuing without MongoDB connection...');
    });

// --- JWT Secret (for session tokens) ---
const JWT_SECRET = process.env.JWT_SECRET || 'your_super_secret_jwt_key_for_digital_commonwealth';

// --- Global DAO Config Cache & Loader ---
// We'll load the DAOConfig once on startup and keep it in memory for quick access.
// This simulates fetching immutable blockchain state.
let globalDaoConfig = null;

const loadDAOConfig = async () => {
    try {
        globalDaoConfig = await DAOConfig.findById('global_dao_config');
        if (globalDaoConfig) {
            console.log('DAOConfig loaded successfully.');
        } else {
            console.warn('DAOConfig document not found. It needs to be initialized via POST /api/dao-config/initialize.');
        }
    } catch (error) {
        console.error("Failed to load DAOConfig on startup:", error);
    }
};

// --- Genesis Token Distribution Function ---
// Processes genesis allocations and distributes initial tokens to users
const processGenesisAllocations = async (daoConfig) => {
    try {
        console.log('Processing genesis allocations...');
        let totalDistributed = 0;
        let successfulAllocations = 0;

        for (const allocation of daoConfig.genesisAllocations) {
            try {
                // Find the user by public key
                const user = await User.findById(allocation.publicKey);
                if (!user) {
                    console.warn(`Genesis allocation failed: User with public key ${allocation.publicKey} not found`);
                    continue;
                }

                // Check if user already has tokens (avoid double allocation)
                if (user.reputationScore > 0) {
                    console.log(`User ${user.username} already has ${user.reputationScore} tokens, skipping genesis allocation`);
                    continue;
                }

                // Update user's reputation score with allocated tokens
                const oldScore = user.reputationScore;
                user.reputationScore = allocation.amount;
                await user.save();

                // Record the transaction for audit purposes
                await Transaction.recordTransaction({
                    type: 'genesis_allocation',
                    from: {
                        publicKey: null,
                        username: 'SYSTEM'
                    },
                    to: {
                        publicKey: user.publicKey,
                        username: user.username
                    },
                    amount: allocation.amount,
                    balances: {
                        fromBefore: null,
                        fromAfter: null,
                        toBefore: oldScore,
                        toAfter: user.reputationScore
                    },
                    memo: 'Genesis allocation from DAO initialization',
                    reason: 'Initial token distribution',
                    signature: 'GENESIS_ALLOCATION',
                    signedMessage: `GENESIS:${user.publicKey}:${allocation.amount}`,
                    executedBy: {
                        publicKey: daoConfig.ownerPublicKey,
                        username: 'DAO_FOUNDER'
                    }
                });

                totalDistributed += allocation.amount;
                successfulAllocations++;

                console.log(`Genesis allocation: ${allocation.amount} ${daoConfig.nativeCoinSymbol} distributed to ${user.username} (${allocation.publicKey})`);
                console.log(`Event: TokensDistributed - User: ${user.username}, Amount: ${allocation.amount}, Type: Genesis_Allocation`);

            } catch (allocationError) {
                console.error(`Error processing genesis allocation for ${allocation.publicKey}:`, allocationError);
            }
        }

        console.log(`Genesis allocation complete: ${successfulAllocations} allocations processed, ${totalDistributed} ${daoConfig.nativeCoinSymbol} distributed`);
        return { successfulAllocations, totalDistributed };

    } catch (error) {
        console.error('Error processing genesis allocations:', error);
        throw error;
    }
};

loadDAOConfig(); // Call once on application start

// --- Middleware for authenticating requests ---
// Extracts publicKey from JWT token, attaches to req.user
const authenticateToken = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) return res.status(401).json({ message: "No authentication token provided." });

    jwt.verify(token, JWT_SECRET, (err, userPayload) => {
        if (err) return res.status(403).json({ message: "Invalid or expired authentication token." });
        // userPayload should contain { publicKey, username }
        req.user = userPayload; 
        next();
    });
};

// --- Middleware to check if the authenticated user is the DAO owner ---
// Requires authenticateToken to run before it.
const authorizeDAOOwner = async (req, res, next) => {
    if (!globalDaoConfig) {
        return res.status(503).json({ message: "DAO configuration not initialized. Please try again later." });
    }
    if (req.user.publicKey !== globalDaoConfig.ownerPublicKey) {
        return res.status(403).json({ message: "Unauthorized. Only the DAO owner can perform this action." });
    }
    next();
};

// --- Middleware to check if user holds minimum tokens (simulates 'onlyTokenHolder') ---
// This middleware explicitly uses the User model to check reputationScore.
const checkMinTokens = async (req, res, next) => {
    try {
        const currentUser = await User.findById(req.user.publicKey); // Explicitly using User model here
        if (!currentUser) {
            return res.status(404).json({ message: "Authenticated user not found." });
        }
        const daoConfig = globalDaoConfig; // Use the cached globalDaoConfig
        if (!daoConfig) {
             console.error('DAOConfig not loaded in cache. This should not happen if initialization is successful.');
             return res.status(503).json({ message: "Server configuration error: DAOConfig not available." });
        }
        if (currentUser.reputationScore < daoConfig.minProposalCreationTokens) {
            return res.status(403).json({ message: `Insufficient tokens. Requires at least ${daoConfig.minProposalCreationTokens} ${daoConfig.nativeCoinSymbol}.` });
        }
        next();
    } catch (error) {
        console.error("Error checking token balance in checkMinTokens middleware:", error);
        res.status(500).json({ message: "Internal server error during token balance check." });
    }
};

// --- API ROUTES FOR USER IDENTITY ---

// POST /api/identity/register
// Registers a new user with their public key and username
app.post('/api/identity/register', async (req, res) => {
    const { username, publicKey, profile } = req.body;

    if (!username || !publicKey) {
        return res.status(400).json({ message: "Username and public key are required." });
    }

    try {
        // Check if user already exists
        const existingUser = await User.findById(publicKey);
        if (existingUser) {
            return res.status(409).json({ message: "User with this public key already exists.", user: existingUser });
        }

        // Check if username is taken
        const existingUsername = await User.findOne({ username });
        if (existingUsername) {
            return res.status(409).json({ message: "Username is already taken." });
        }

        // Create new user
        const newUser = new User({
            _id: publicKey,
            username,
            publicKey,
            profile: profile || {},
            reputationScore: 0 // Start with 0 tokens
        });

        await newUser.save();
        console.log(`Event: UserRegistered - Username: ${username}, PublicKey: ${publicKey}`);

        res.status(201).json({
            message: "User registered successfully!",
            user: {
                username: newUser.username,
                publicKey: newUser.publicKey,
                reputationScore: newUser.reputationScore,
                profile: newUser.profile
            }
        });
    } catch (error) {
        console.error("Error registering user:", error);
        res.status(500).json({ message: "Server error during user registration." });
    }
});

// POST /api/identity/login
// Authenticates a user and returns a JWT token
app.post('/api/identity/login', async (req, res) => {
    const { publicKey, signature, message } = req.body;

    if (!publicKey || !signature || !message) {
        return res.status(400).json({ message: "Public key, signature, and message are required." });
    }

    try {
        // Verify the signature
        const isSignatureValid = KeyGen.verifySignature(message, signature, publicKey);
        if (!isSignatureValid) {
            return res.status(401).json({ message: "Invalid signature. Authentication failed." });
        }

        // Find the user
        const user = await User.findById(publicKey);
        if (!user) {
            return res.status(404).json({ message: "User not found. Please register first." });
        }

        if (!user.isActive) {
            return res.status(403).json({ message: "User account is deactivated." });
        }

        // Update last login
        user.lastLogin = new Date();
        await user.save();

        // Generate JWT token
        const token = jwt.sign(
            {
                publicKey: user.publicKey,
                username: user.username,
                roles: user.roles
            },
            JWT_SECRET,
            { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
        );

        console.log(`Event: UserLogin - Username: ${user.username}, PublicKey: ${publicKey}`);

        res.json({
            message: "Login successful!",
            token,
            user: {
                username: user.username,
                publicKey: user.publicKey,
                reputationScore: user.reputationScore,
                profile: user.profile,
                roles: user.roles
            }
        });
    } catch (error) {
        console.error("Error during login:", error);
        res.status(500).json({ message: "Server error during authentication." });
    }
});

// GET /api/identity/profile/:publicKey
// Gets a user's profile information
app.get('/api/identity/profile/:publicKey', async (req, res) => {
    const { publicKey } = req.params;

    try {
        const user = await User.findById(publicKey).select('-metadata');
        if (!user) {
            return res.status(404).json({ message: "User not found." });
        }

        res.json({
            user: {
                username: user.username,
                publicKey: user.publicKey,
                reputationScore: user.reputationScore,
                profile: user.profile,
                governanceStats: user.governanceStats,
                isVerified: user.isVerified,
                roles: user.roles,
                createdAt: user.createdAt
            }
        });
    } catch (error) {
        console.error("Error fetching user profile:", error);
        res.status(500).json({ message: "Server error fetching user profile." });
    }
});

// PUT /api/identity/profile
// Updates the authenticated user's profile
app.put('/api/identity/profile', authenticateToken, async (req, res) => {
    const { profile } = req.body;

    try {
        const user = await User.findById(req.user.publicKey);
        if (!user) {
            return res.status(404).json({ message: "User not found." });
        }

        // Update profile fields
        if (profile) {
            user.profile = { ...user.profile, ...profile };
        }

        await user.save();

        res.json({
            message: "Profile updated successfully!",
            user: {
                username: user.username,
                publicKey: user.publicKey,
                profile: user.profile
            }
        });
    } catch (error) {
        console.error("Error updating profile:", error);
        res.status(500).json({ message: "Server error updating profile." });
    }
});

// --- API ROUTES FOR DAOConfig ---

// POST /api/dao-config/initialize
// Initializes the global DAO configuration. Can only be called once.
// Requires: ownerPublicKey (the initial administrator's pubkey), and a signature from that owner
// over the entire configuration payload to prove original intent.
app.post('/api/dao-config/initialize', async (req, res) => {
    // Prevent re-initialization if config already exists
    if (globalDaoConfig) {
        return res.status(409).json({ message: "DAO configuration already initialized." });
    }

    const {
        ownerPublicKey,
        nativeCoinName,
        nativeCoinSymbol,
        totalSupply,
        initialDistributionStrategy,
        genesisAllocations,
        votingDuration,
        minimumQuorumPercentage,
        minProposalCreationTokens,
        // clientProvidedSignature and messageSignedByOwner are crucial for verification
        clientProvidedSignature,
        messageSignedByOwner // The stringified payload that the owner signed
    } = req.body;

    // Basic validation
    if (!ownerPublicKey || !clientProvidedSignature || !messageSignedByOwner || !nativeCoinName || !nativeCoinSymbol || totalSupply == null || votingDuration == null || minimumQuorumPercentage == null || minProposalCreationTokens == null) {
        return res.status(400).json({ message: "Missing required DAO configuration parameters or signature details." });
    }

    try {
        // 1. Verify the owner's signature over the entire payload
        // This is a critical step: the owner must sign the exact configuration they intend to initialize.
        const isSignatureValid = KeyGen.verifySignature(messageSignedByOwner, clientProvidedSignature, ownerPublicKey);
        if (!isSignatureValid) {
            return res.status(401).json({ message: "Invalid owner signature for initialization." });
        }

        const newConfig = new DAOConfig({
            _id: 'global_dao_config', // Enforces single document
            ownerPublicKey,
            nativeCoinName,
            nativeCoinSymbol,
            totalSupply,
            initialDistributionStrategy,
            genesisAllocations: genesisAllocations || [], // Ensure it's an array
            votingDuration,
            minimumQuorumPercentage,
            minProposalCreationTokens
        });

        const savedConfig = await newConfig.save();
        globalDaoConfig = savedConfig; // Update in-memory cache

        // Process genesis allocations to distribute initial tokens
        try {
            const allocationResult = await processGenesisAllocations(savedConfig);
            console.log(`Event: DAOConfigInitialized - Owner: ${ownerPublicKey}, Total Supply: ${totalSupply}`);
            console.log(`Genesis allocations processed: ${allocationResult.successfulAllocations} users, ${allocationResult.totalDistributed} tokens distributed`);

            res.status(201).json({
                message: "DAO configuration initialized successfully.",
                config: savedConfig,
                genesisDistribution: allocationResult
            });
        } catch (allocationError) {
            console.error('Error processing genesis allocations:', allocationError);
            // DAO config was saved successfully, but genesis allocation failed
            res.status(201).json({
                message: "DAO configuration initialized successfully, but genesis allocation encountered errors.",
                config: savedConfig,
                warning: "Some genesis allocations may not have been processed. Check server logs."
            });
        }

    } catch (error) {
        // Handle cases where _id 'global_dao_config' already exists (due to unique:true)
        if (error.code === 11000) { // MongoDB duplicate key error
            return res.status(409).json({ message: "DAO configuration has already been initialized." });
        }
        console.error("Error initializing DAO config:", error);
        res.status(500).json({ message: "Server error during DAO configuration initialization." });
    }
});

// GET /api/dao-config
// Retrieves the current global DAO configuration. Publicly accessible.
app.get('/api/dao-config', (req, res) => {
    if (!globalDaoConfig) {
        return res.status(404).json({ message: "DAO configuration not found. It needs to be initialized." });
    }
    res.json({ config: globalDaoConfig });
});

// PUT /api/dao-config/update
// Updates the global DAO configuration. Only accessible by the ownerPublicKey
// and requires a signed message from the owner.
app.put('/api/dao-config/update', authenticateToken, authorizeDAOOwner, async (req, res) => {
    if (!globalDaoConfig) {
        return res.status(404).json({ message: "DAO configuration not found. It needs to be initialized." });
    }

    const {
        // Parameters allowed to be updated (e.g., votingDuration, minProposalCreationTokens, quorum)
        // ownerPublicKey should NOT be updated via this route, if ever, it requires a separate governance proposal.
        votingDuration,
        minimumQuorumPercentage,
        minProposalCreationTokens,
        // Add other updatable fields as needed.
        // And the signature for the update payload
        clientProvidedSignature,
        messageSignedByOwner // The stringified payload that the owner signed
    } = req.body;

    if (!clientProvidedSignature || !messageSignedByOwner) {
        return res.status(400).json({ message: "Missing owner signature for update." });
    }

    try {
        // Construct the message that was signed by the client for verification
        // This should be the exact subset of fields being updated, stringified.
        const updatePayloadToVerify = JSON.stringify({
            votingDuration,
            minimumQuorumPercentage,
            minProposalCreationTokens,
            // ... include any other fields that are being updated
        });

        const isSignatureValid = KeyGen.verifySignature(updatePayloadToVerify, clientProvidedSignature, req.user.publicKey);
        if (!isSignatureValid) {
            return res.status(401).json({ message: "Invalid owner signature for update." });
        }

        // Apply updates
        if (votingDuration != null) globalDaoConfig.votingDuration = votingDuration;
        if (minimumQuorumPercentage != null) globalDaoConfig.minimumQuorumPercentage = minimumQuorumPercentage;
        if (minProposalCreationTokens != null) globalDaoConfig.minProposalCreationTokens = minProposalCreationTokens;
        // ... apply other updates similarly

        const updatedConfig = await globalDaoConfig.save();
        // globalDaoConfig is already updated by reference due to Mongoose save

        console.log(`Event: DAOConfigUpdated - By: ${req.user.publicKey}`);
        res.json({ message: "DAO configuration updated successfully.", config: updatedConfig });

    } catch (error) {
        console.error("Error updating DAO config:", error);
        res.status(500).json({ message: "Server error during DAO configuration update." });
    }
});

// --- API ROUTES FOR TOKEN MANAGEMENT ---

// POST /api/tokens/mint
// Mints new tokens and distributes them to specified users (DAO owner only)
app.post('/api/tokens/mint', authenticateToken, authorizeDAOOwner, async (req, res) => {
    const { recipients, reason, clientProvidedSignature, messageSignedByOwner } = req.body;

    if (!recipients || !Array.isArray(recipients) || recipients.length === 0) {
        return res.status(400).json({ message: "Recipients array is required and must not be empty." });
    }

    if (!clientProvidedSignature || !messageSignedByOwner) {
        return res.status(400).json({ message: "Owner signature is required for token minting." });
    }

    try {
        // Verify the owner's signature over the minting request
        const isSignatureValid = KeyGen.verifySignature(messageSignedByOwner, clientProvidedSignature, req.user.publicKey);
        if (!isSignatureValid) {
            return res.status(401).json({ message: "Invalid owner signature for token minting." });
        }

        const results = [];
        let totalMinted = 0;

        for (const recipient of recipients) {
            const { publicKey, amount } = recipient;

            if (!publicKey || amount == null || amount <= 0) {
                results.push({ publicKey, success: false, error: "Invalid recipient data" });
                continue;
            }

            try {
                const user = await User.findById(publicKey);
                if (!user) {
                    results.push({ publicKey, success: false, error: "User not found" });
                    continue;
                }

                const oldBalance = user.reputationScore;
                user.reputationScore += amount;
                await user.save();

                totalMinted += amount;
                results.push({
                    publicKey,
                    username: user.username,
                    success: true,
                    amount,
                    oldBalance,
                    newBalance: user.reputationScore
                });

                console.log(`Event: TokensMinted - User: ${user.username}, Amount: ${amount}, Reason: ${reason || 'Admin mint'}`);

            } catch (userError) {
                console.error(`Error minting tokens for ${publicKey}:`, userError);
                results.push({ publicKey, success: false, error: "Database error" });
            }
        }

        console.log(`Token minting complete: ${totalMinted} ${globalDaoConfig.nativeCoinSymbol} minted to ${results.filter(r => r.success).length} users`);

        res.json({
            message: "Token minting completed.",
            totalMinted,
            coinSymbol: globalDaoConfig.nativeCoinSymbol,
            results,
            reason: reason || 'Admin mint'
        });

    } catch (error) {
        console.error("Error during token minting:", error);
        res.status(500).json({ message: "Server error during token minting." });
    }
});

// POST /api/tokens/distribute
// Distributes tokens from the DAO treasury to users (DAO owner only)
app.post('/api/tokens/distribute', authenticateToken, authorizeDAOOwner, async (req, res) => {
    const { publicKey, amount, reason, clientProvidedSignature, messageSignedByOwner } = req.body;

    if (!publicKey || amount == null || amount <= 0) {
        return res.status(400).json({ message: "Valid public key and positive amount are required." });
    }

    if (!clientProvidedSignature || !messageSignedByOwner) {
        return res.status(400).json({ message: "Owner signature is required for token distribution." });
    }

    try {
        // Verify the owner's signature
        const isSignatureValid = KeyGen.verifySignature(messageSignedByOwner, clientProvidedSignature, req.user.publicKey);
        if (!isSignatureValid) {
            return res.status(401).json({ message: "Invalid owner signature for token distribution." });
        }

        // Find the recipient user
        const user = await User.findById(publicKey);
        if (!user) {
            return res.status(404).json({ message: "Recipient user not found." });
        }

        // Update user's token balance
        const oldBalance = user.reputationScore;
        user.reputationScore += amount;
        await user.save();

        console.log(`Event: TokensDistributed - User: ${user.username}, Amount: ${amount}, Reason: ${reason || 'Admin distribution'}`);

        res.json({
            message: "Tokens distributed successfully.",
            recipient: {
                username: user.username,
                publicKey: user.publicKey,
                oldBalance,
                newBalance: user.reputationScore,
                amountReceived: amount
            },
            coinSymbol: globalDaoConfig.nativeCoinSymbol,
            reason: reason || 'Admin distribution'
        });

    } catch (error) {
        console.error("Error during token distribution:", error);
        res.status(500).json({ message: "Server error during token distribution." });
    }
});

// GET /api/tokens/balance/:publicKey
// Gets the token balance for a specific user
app.get('/api/tokens/balance/:publicKey', async (req, res) => {
    const { publicKey } = req.params;

    try {
        const user = await User.findById(publicKey);
        if (!user) {
            return res.status(404).json({ message: "User not found." });
        }

        res.json({
            user: {
                username: user.username,
                publicKey: user.publicKey,
                balance: user.reputationScore,
                coinSymbol: globalDaoConfig?.nativeCoinSymbol || 'CMC'
            }
        });

    } catch (error) {
        console.error("Error fetching token balance:", error);
        res.status(500).json({ message: "Server error fetching token balance." });
    }
});

// GET /api/tokens/supply
// Gets information about the total token supply and distribution
app.get('/api/tokens/supply', async (req, res) => {
    try {
        if (!globalDaoConfig) {
            return res.status(404).json({ message: "DAO configuration not found." });
        }

        // Calculate circulating supply by summing all user balances
        const users = await User.find({}, 'reputationScore');
        const circulatingSupply = users.reduce((total, user) => total + user.reputationScore, 0);

        // Get top holders
        const topHolders = await User.find({ reputationScore: { $gt: 0 } })
            .sort({ reputationScore: -1 })
            .limit(10)
            .select('username publicKey reputationScore');

        res.json({
            tokenInfo: {
                name: globalDaoConfig.nativeCoinName,
                symbol: globalDaoConfig.nativeCoinSymbol,
                totalSupply: globalDaoConfig.totalSupply,
                circulatingSupply,
                remainingSupply: globalDaoConfig.totalSupply - circulatingSupply
            },
            distribution: {
                totalHolders: users.filter(u => u.reputationScore > 0).length,
                topHolders: topHolders.map(user => ({
                    username: user.username,
                    publicKey: user.publicKey,
                    balance: user.reputationScore,
                    percentage: ((user.reputationScore / circulatingSupply) * 100).toFixed(2)
                }))
            }
        });

    } catch (error) {
        console.error("Error fetching token supply info:", error);
        res.status(500).json({ message: "Server error fetching token supply information." });
    }
});

// POST /api/tokens/transfer
// Transfers tokens between users with cryptographic signature verification
app.post('/api/tokens/transfer', authenticateToken, async (req, res) => {
    const { recipientPublicKey, amount, memo, clientProvidedSignature, messageSignedBySender } = req.body;
    const senderPublicKey = req.user.publicKey;

    if (!recipientPublicKey || amount == null || amount <= 0) {
        return res.status(400).json({ message: "Valid recipient public key and positive amount are required." });
    }

    if (!clientProvidedSignature || !messageSignedBySender) {
        return res.status(400).json({ message: "Sender signature is required for token transfer." });
    }

    if (senderPublicKey === recipientPublicKey) {
        return res.status(400).json({ message: "Cannot transfer tokens to yourself." });
    }

    try {
        // Verify the sender's signature over the transfer details
        const isSignatureValid = KeyGen.verifySignature(messageSignedBySender, clientProvidedSignature, senderPublicKey);
        if (!isSignatureValid) {
            return res.status(401).json({ message: "Invalid sender signature for token transfer." });
        }

        // Find sender and recipient users
        const [sender, recipient] = await Promise.all([
            User.findById(senderPublicKey),
            User.findById(recipientPublicKey)
        ]);

        if (!sender) {
            return res.status(404).json({ message: "Sender user not found." });
        }

        if (!recipient) {
            return res.status(404).json({ message: "Recipient user not found." });
        }

        // Check if sender has sufficient balance
        if (sender.reputationScore < amount) {
            return res.status(400).json({
                message: "Insufficient balance for transfer.",
                currentBalance: sender.reputationScore,
                requestedAmount: amount
            });
        }

        // Perform the transfer
        const senderOldBalance = sender.reputationScore;
        const recipientOldBalance = recipient.reputationScore;

        sender.reputationScore -= amount;
        recipient.reputationScore += amount;

        // Save both users
        await Promise.all([sender.save(), recipient.save()]);

        // Record the transaction for audit purposes
        await Transaction.recordTransaction({
            type: 'user_transfer',
            from: {
                publicKey: senderPublicKey,
                username: sender.username
            },
            to: {
                publicKey: recipientPublicKey,
                username: recipient.username
            },
            amount: amount,
            balances: {
                fromBefore: senderOldBalance,
                fromAfter: sender.reputationScore,
                toBefore: recipientOldBalance,
                toAfter: recipient.reputationScore
            },
            memo: memo || '',
            reason: 'User-to-user transfer',
            signature: clientProvidedSignature,
            signedMessage: messageSignedBySender,
            executedBy: {
                publicKey: senderPublicKey,
                username: sender.username
            }
        });

        console.log(`Event: TokensTransferred - From: ${sender.username} (${senderPublicKey}), To: ${recipient.username} (${recipientPublicKey}), Amount: ${amount}`);

        res.json({
            message: "Token transfer completed successfully.",
            transfer: {
                from: {
                    username: sender.username,
                    publicKey: senderPublicKey,
                    oldBalance: senderOldBalance,
                    newBalance: sender.reputationScore
                },
                to: {
                    username: recipient.username,
                    publicKey: recipientPublicKey,
                    oldBalance: recipientOldBalance,
                    newBalance: recipient.reputationScore
                },
                amount,
                memo: memo || '',
                coinSymbol: globalDaoConfig?.nativeCoinSymbol || 'CMC',
                timestamp: new Date().toISOString()
            }
        });

    } catch (error) {
        console.error("Error during token transfer:", error);
        res.status(500).json({ message: "Server error during token transfer." });
    }
});

// GET /api/tokens/transfers/:publicKey
// Gets transfer history for a specific user
app.get('/api/tokens/transfers/:publicKey', async (req, res) => {
    const { publicKey } = req.params;
    const { limit = 50, offset = 0, type = null } = req.query;

    try {
        const user = await User.findById(publicKey);
        if (!user) {
            return res.status(404).json({ message: "User not found." });
        }

        // Get transaction history using the Transaction model
        const { transactions, total } = await Transaction.getUserTransactions(publicKey, {
            limit,
            offset,
            type
        });

        // Format transactions for response
        const formattedTransactions = transactions.map(tx => ({
            id: tx._id,
            type: tx.type,
            amount: tx.amount,
            from: tx.from,
            to: tx.to,
            memo: tx.memo,
            reason: tx.reason,
            status: tx.status,
            timestamp: tx.createdAt,
            balances: tx.balances,
            // Determine if this user was sender or receiver
            direction: tx.from.publicKey === publicKey ? 'sent' : 'received'
        }));

        res.json({
            user: {
                username: user.username,
                publicKey: user.publicKey,
                currentBalance: user.reputationScore
            },
            transactions: formattedTransactions,
            pagination: {
                limit: parseInt(limit),
                offset: parseInt(offset),
                total
            },
            coinSymbol: globalDaoConfig?.nativeCoinSymbol || 'CMC'
        });

    } catch (error) {
        console.error("Error fetching transfer history:", error);
        res.status(500).json({ message: "Server error fetching transfer history." });
    }
});

// GET /api/tokens/transactions/stats
// Gets overall transaction statistics
app.get('/api/tokens/transactions/stats', async (req, res) => {
    try {
        const stats = await Transaction.getTransactionStats();

        res.json({
            statistics: stats,
            coinSymbol: globalDaoConfig?.nativeCoinSymbol || 'CMC'
        });

    } catch (error) {
        console.error("Error fetching transaction statistics:", error);
        res.status(500).json({ message: "Server error fetching transaction statistics." });
    }
});

// --- Other API routes (Identity, Governance, EAC) would be defined here ---

// POST /api/governance/proposals (createProposal)
// Requires authentication and minimum token balance (now explicitly using User)
app.post('/api/governance/proposals', authenticateToken, checkMinTokens, async (req, res) => {
    const { title, description, clientProvidedAuthorSignature, messageSignedByAuthor, category, relatedEntityId } = req.body;
    const authorPublicKey = req.user.publicKey; // From authenticated token payload

    if (!title || !description || !clientProvidedAuthorSignature || !messageSignedByAuthor) {
        return res.status(400).json({ message: "Title, description, author signature, and signed message are required." });
    }

    try {
        // 1. Verify the author's signature over the proposal content
        // The messageSignedByAuthor should be the exact string that the client signed (e.g., title + description).
        const isAuthorSignatureValid = KeyGen.verifySignature(messageSignedByAuthor, clientProvidedAuthorSignature, authorPublicKey);
        if (!isAuthorSignatureValid) {
            return res.status(401).json({ message: "Invalid author signature. Proposal creation failed." });
        }

        // Calculate proposalHash on server side for consistency with schema pre-save hook
        const contentToHash = title + description;
        const msgUint8 = KeyGen.encodeUTF8(contentToHash);
        const proposalHash = crypto.createHash('sha256').update(msgUint8).digest('base64');

        // Check for duplicate proposal content (hash) to prevent spam
        const existingProposal = await GovernanceProposal.findOne({ proposalHash });
        if (existingProposal) {
            return res.status(409).json({ message: "A proposal with this exact content already exists." });
        }

        // Get DAO config for voting duration
        const daoConfig = globalDaoConfig;
        if (!daoConfig) {
             console.error('DAOConfig not loaded. Please initialize it.');
             return res.status(500).json({ message: "Server configuration error: DAOConfig not available." });
        }

        const now = Math.floor(Date.now() / 1000); // Current Unix timestamp in seconds
        const votingDurationSeconds = daoConfig.votingDuration;

        const newProposal = new GovernanceProposal({
            title,
            description,
            authorPublicKey,
            authorSignature: clientProvidedAuthorSignature, // Store the client-provided signature
            proposalHash,
            status: 'Active', // Proposals are active immediately after creation
            startTime: now,
            endTime: now + votingDurationSeconds,
            threshold: daoConfig.minimumQuorumPercentage, // Store quorum as percentage
            category,
            relatedEntityId
        });

        await newProposal.save();
        // Emit ProposalCreated event (log to console for now, future Kafka/RabbitMQ)
        console.log(`Event: ProposalCreated - ID: ${newProposal._id}, Proposer: ${authorPublicKey}, Title: "${title}"`);

        res.status(201).json({ message: "Proposal created successfully!", proposal: newProposal });
    } catch (error) {
        console.error("Error creating proposal:", error);
        res.status(500).json({ message: error.message || "Server error creating proposal." });
    }
});

// Placeholder for other routes (to be imported/defined, e.g., Identity, EAC)
app.get('/', (req, res) => {
    res.send('Digital Commonwealth Backend Running! Access APIs at /api/...');
});

// Start the server
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
    console.log(`Digital Commonwealth API server listening on port ${PORT}`);
    console.log(`Test endpoint available at http://localhost:${PORT}/test`);

    // --- Client-side example to initialize as founder (Moved here) ---
    // This function will attempt to initialize the DAO config once the server is ready.
    const initializeDAOAsFounder = async () => {
        try {
            console.log('Attempting to initialize DAO config as founder...');
            const myKeypair = await KeyGen.generateHDWallet();
            const founderPublicKey = myKeypair.publicKeyBase58Nacl;

            // --- Register Founder as a User first ---
            // This is a crucial step to ensure the founder's public key exists in the User model,
            // which is a prerequisite for JWT authentication in subsequent requests (like creating proposals).
            try {
                const userResponse = await fetch(`http://localhost:${PORT}/api/identity/register`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        username: 'FounderUser', // A default username for the founder
                        publicKey: founderPublicKey
                    })
                });
                const userData = await userResponse.json();
                if (userResponse.ok) {
                    console.log('Founder User registered successfully (or already exists):', userData.user.username);
                    // Optionally, if this is the very first run, mint initial tokens to the founder.
                    // This would ideally be a separate admin function or part of a genesis script.
                    // For now, we assume reputationScore starts at 0 and is increased by other means.
                } else if (userResponse.status === 409) {
                    console.log('Founder User already registered.');
                } else {
                    console.error('Founder User registration failed:', userData.message);
                    return; // Stop if registration fails
                }
            } catch (userRegError) {
                console.error('Error during Founder User registration:', userRegError);
                return; // Stop if registration fails
            }


            const configPayload = {
                ownerPublicKey: founderPublicKey,
                nativeCoinName: "Commonwealth Coin",
                nativeCoinSymbol: "CMC",
                totalSupply: 1000000000,
                initialDistributionStrategy: "Genesis_Allocation",
                genesisAllocations: [
                    { publicKey: founderPublicKey, amount: 100000000 } // 10% to founder
                ],
                votingDuration: 604800, // 7 days in seconds
                minimumQuorumPercentage: 0.25,
                minProposalCreationTokens: 10000
            };

            // Sign the payload using the founder's private key
            const messageToSign = JSON.stringify(configPayload);
            const signature = KeyGen.signMessage(messageToSign, myKeypair.privateKeyNacl);

            // Send the request to initialize the DAO config
            const response = await fetch(`http://localhost:${PORT}/api/dao-config/initialize`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    ...configPayload,
                    clientProvidedSignature: signature,
                    messageSignedByOwner: messageToSign
                })
            });

            const result = await response.json();
            if (response.ok) {
                console.log('DAO Initialization successful:', result);
            } else {
                console.error('DAO Initialization failed:', result.message);
            }
        } catch (error) {
            console.error('Error during DAO initialization example:', error);
        }
    };

    // Call the initialization example after the server starts.
    initializeDAOAsFounder();

    // Add a test endpoint
    app.get('/test', (req, res) => {
        res.json({ message: 'Server is running!' });
    });
});

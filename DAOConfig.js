import mongoose from 'mongoose';

const daoConfigSchema = new mongoose.Schema({
    _id: {
        type: String,
        default: 'global_dao_config',
        required: true,
        unique: true
    },
    contractName: {
        type: String,
        required: true,
        default: "DigitalCommonwealthDAO"
    },
    version: {
        type: String,
        required: true,
        default: "1.0.0"
    },
    ownerPublicKey: { // Initial deployer or administrative public key (KeyGen derived)
        type: String,
        required: true
    },
    // *** Native Coin Properties ***
    nativeCoinName: { // Explicitly define the native coin's full name
        type: String,
        required: true,
        default: "Commonwealth Coin"
    },
    nativeCoinSymbol: { // Explicitly define the native coin's symbol
        type: String,
        required: true,
        default: "CMC" // Renamed from CGT to CMC for 'Commonwealth Coin'
    },
    totalSupply: { // Total supply of your native coin
        type: Number,
        required: true,
        default: 1000000000, // Example: 1 Billion CMC (adjust as needed)
        min: 0
    },
    // Initial distribution strategy of the native coin
    // This could dictate how initial reputationScore are assigned
    initialDistributionStrategy: {
        type: String,
        enum: ['Genesis_Allocation', 'Airdrop', 'Contribution_Reward', 'Hybrid'],
        default: 'Genesis_Allocation'
    },
    // Initial allocation details (e.g., to a treasury address, founding members)
    genesisAllocations: [{
        publicKey: { type: String, required: true },
        amount: { type: Number, required: true, min: 0 }
    }],
    // *** Governance Parameters (powered by the native coin) ***
    votingDuration: {
        type: Number,
        required: true,
        default: 604800, // 7 days in seconds
        min: 0
    },
    minimumQuorumPercentage: {
        type: Number,
        required: true,
        default: 0.25, // 25% of total supply (or total cast votes, depending on exact rule)
        min: 0,
        max: 1
    },
    minProposalCreationTokens: {
        type: Number,
        default: 10000, // Example: 10,000 CMC needed to propose
        min: 0
    }
}, { timestamps: true });

daoConfigSchema.pre('save', function(next) {
    if (this.isNew && this._id !== 'global_dao_config') {
        return next(new Error('Only one global DAOConfig document allowed with _id "global_dao_config".'));
    }
    next();
});

const DAOConfig = mongoose.model('DAOConfig', daoConfigSchema);

export default DAOConfig;
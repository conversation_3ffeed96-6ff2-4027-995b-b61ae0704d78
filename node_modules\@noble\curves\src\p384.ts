/**
 * NIST secp384r1 aka p384.
 * @module
 */
/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */
import { type H2CMethod } from './abstract/hash-to-curve.ts';
import { p384_hasher, p384 as p384n } from './nist.ts';
export const p384: typeof p384n = p384n;
export const secp384r1: typeof p384n = p384n;
export const hashToCurve: H2CMethod<bigint> = /* @__PURE__ */ (() => p384_hasher.hashToCurve)();
export const encodeToCurve: H2CMethod<bigint> = /* @__PURE__ */ (() => p384_hasher.encodeToCurve)();

/** @deprecated Use `import { p384_hasher } from "@noble/curves/nist"` module. */

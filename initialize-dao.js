import { KeyGen } from './KeyGen.js';

async function initializeDAO() {
    try {
        // Generate a new keypair for the founder
        const myKeypair = await KeyGen.generateHDWallet();
        const founderPublicKey = myKeypair.publicKeyBase58Nacl;
        
        console.log("Generated founder keypair:");
        console.log("Public Key:", founderPub<PERSON><PERSON><PERSON>);
        console.log("Seed Phrase (KEEP SECURE):", myKeypair.seedPhrase);
        
        const configPayload = {
            ownerPublicKey: founderPublic<PERSON>ey,
            nativeCoinName: "Commonwealth Coin",
            nativeCoinSymbol: "CMC",
            totalSupply: 1000000000,
            initialDistributionStrategy: "Genesis_Allocation",
            genesisAllocations: [
                { publicKey: founderPublic<PERSON>ey, amount: 100000000 } // 10% to founder
            ],
            votingDuration: 604800, // 7 days in seconds
            minimumQuorumPercentage: 0.25,
            minProposalCreationTokens: 10000
        };
        
        // Sign the payload
        const messageToSign = JSON.stringify(configPayload);
        const signature = KeyGen.signMessage(messageToSign, myKeypair.privateKeyNacl);
        
        // Send the request
        console.log("Sending request to initialize DAO...");
        const response = await fetch('http://localhost:3000/api/dao-config/initialize', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                ...configPayload,
                clientProvidedSignature: signature,
                messageSignedByOwner: messageToSign
            })
        });
        
        const result = await response.json();
        console.log("DAO Initialization Result:", result);
    } catch (error) {
        console.error("Error initializing DAO:", error);
    }
}

// Run the initialization
initializeDAO();
import nacl from 'tweetnacl';
import bs58 from 'bs58';
import bip39 from 'bip39';
import { <PERSON>Key } from '@scure/bip32';

/**
 * @class KeyGen
 * @description Provides a comprehensive set of static methods for cryptographic key
 * generation, derivation, signing, and verification, essential for
 * building decentralized identities and securing transactions.
 * It leverages BIP39 for mnemonic phrases, BIP32 for hierarchical
 * deterministic key derivation, and Ed25519 (via tweetnacl) for
 * digital signatures.
 */
class KeyGen {

    /**
     * Converts a Uint8Array key to a Base58 string.
     * Base58 encoding is commonly used in cryptocurrencies to represent
     * addresses and keys more compactly and legibly than raw bytes.
     * @param {Uint8Array} key - The key (public key, private key, or signature) to convert.
     * @returns {string} The Base58 encoded string.
     */
    static convertToBase58(key) {
        return bs58.encode(key);
    }

    /**
     * Converts a Base58 string back to a Uint8Array.
     * This is the inverse of convertToBase58.
     * @param {string} base58String - The Base58 string to decode.
     * @returns {Uint8Array} The decoded key.
     */
    static convertFromBase58(base58String) {
        return bs58.decode(base58String);
    }

    /**
     * Encodes a string message to a Uint8Array using UTF-8.
     * Cryptographic functions typically operate on byte arrays, so this
     * utility is necessary before signing or verifying string messages.
     * @param {string} message - The message string.
     * @returns {Uint8Array} The UTF-8 encoded message.
     */
    static encodeUTF8(message) {
        const encoder = new TextEncoder();
        return encoder.encode(message);
    }

    /**
     * Signs a message using a secret key.
     * This method uses the tweetnacl library for Ed25519 signing,
     * which is a modern, high-performance elliptic curve cryptography system
     * suitable for digital signatures.
     * @param {string} message - The message to sign.
     * @param {Uint8Array} secretKey - The secret key (Uint8Array) for signing.
     * For Ed25519, this is typically 64 bytes (32-byte private key + 32-byte public key).
     * @returns {string} The Base58 encoded signature.
     */
    static signMessage(message, secretKey) {
        const messageUint8 = KeyGen.encodeUTF8(message);
        const signature = nacl.sign.detached(messageUint8, secretKey);
        return KeyGen.convertToBase58(signature);
    }

    /**
     * Verifies a message signature using a public key.
     * This uses the tweetnacl library for Ed25519 signature verification.
     * @param {string} message - The original message.
     * @param {string} signatureBase58 - The Base58 encoded signature.
     * @param {string} publicKeyBase58 - The Base58 encoded public key.
     * @returns {boolean} True if the signature is valid, false otherwise.
     */
    static verifySignature(message, signatureBase58, publicKeyBase58) {
        const messageUint8 = KeyGen.encodeUTF8(message);
        const signature = KeyGen.convertFromBase58(signatureBase58);
        const publicKey = KeyGen.convertFromBase58(publicKeyBase58);

        try {
            // Attempt verification directly
            return nacl.sign.detached.verify(messageUint8, signature, publicKey);
        } catch (error) {
            // This specific error handling block addresses a potential mismatch if a
            // BIP32 public key (33 bytes for compressed secp256k1) is accidentally
            // passed to nacl, which expects a 32-byte Ed25519 public key.
            // In a production system, it's crucial to ensure the correct key type
            // (the Ed25519 publicKeyNacl from generateHDMasterNode/deriveChildKey/deriveFromPath)
            // is always used for nacl functions. This catch block acts as a temporary
            // safeguard or debugging aid for such mismatches.
            if (error.message === 'bad public key size' && publicKey.length > 32) {
                // Adjust by taking the first 32 bytes. This is a heuristic
                // and should be replaced by ensuring correct key type usage upstream.
                const adjustedPublicKey = publicKey.slice(0, 32);
                return nacl.sign.detached.verify(messageUint8, signature, adjustedPublicKey);
            }
            // Re-throw other unexpected errors
            throw error;
        }
    }

    /**
     * Generates a new random mnemonic seed phrase (BIP39).
     * Mnemonic phrases provide a human-readable way to back up and restore
     * a hierarchical deterministic (HD) wallet.
     * @returns {string} The generated mnemonic phrase (e.g., "word1 word2 ... word12").
     */
    static generateMnemonicSeed() {
        // Default strength is 128 bits (12 words)
        return bip39.generateMnemonic();
    }

    /**
     * Generates a master HD node from a BIP39 mnemonic seed phrase.
     * This is the root of the HD key tree, allowing for the derivation of
     * an unlimited number of child keys from a single seed.
     * It also derives an Ed25519 keypair from the master node's private key
     * for direct use with tweetnacl, bridging BIP32 (secp256k1) and Ed25519.
     * @param {string} mnemonicPhrase - The BIP39 mnemonic seed phrase.
     * @param {string} [passphrase=''] - Optional BIP39 passphrase (for added security, if used).
     * @returns {Object} An object containing the master HD node and its key details.
     * Includes both BIP32 (publicKeyBip32, privateKeyBip32) and
     * Ed25519 (publicKeyNacl, privateKeyNacl) key representations.
     * @throws {Error} If the master node's private key is null, which would prevent Ed25519 derivation.
     */
    static async generateHDMasterNode(mnemonicPhrase, passphrase = '') {
        // Convert mnemonic to a 64-byte seed (BIP39 specifies 512-bit seed from mnemonic and passphrase)
        const seed = await bip39.mnemonicToSeed(mnemonicPhrase, passphrase);

        // Create master HD node from the 64-byte seed
        const masterNode = HDKey.fromMasterSeed(seed);

        // Derive an Ed25519 keypair from the master HD node's *private key* for tweetnacl.
        // This is crucial for signing with nacl from an HD derived key.
        // The BIP32 private key is 32 bytes, which serves as a good seed for Ed25519.
        if (!masterNode.privateKey) {
            throw new Error("Master node private key is null. Cannot derive Ed25519 keypair.");
        }
        const naclKeyPair = nacl.sign.keyPair.fromSeed(masterNode.privateKey);


        return {
            node: masterNode, // The HDKey instance from @scure/bip32
            // BIP32 key properties (typically secp256k1 curve)
            publicKeyBip32: masterNode.publicKey, // Typically 33 bytes compressed secp256k1
            privateKeyBip32: masterNode.privateKey, // 32 bytes secp256k1 scalar
            publicKeyBase58Bip32: KeyGen.convertToBase58(masterNode.publicKey),
            privateKeyBase58Bip32: masterNode.privateKey ? KeyGen.convertToBase58(masterNode.privateKey) : null,
            chainCode: masterNode.chainCode, // 32 bytes, used for deriving child keys
            chainCodeBase58: KeyGen.convertToBase58(masterNode.chainCode),
            fingerprint: masterNode.fingerprint,
            index: masterNode.index,
            depth: masterNode.depth,
            parentFingerprint: masterNode.parentFingerprint,
            // Ed25519 key properties for tweetnacl compatibility (derived from BIP32 private key)
            publicKeyNacl: naclKeyPair.publicKey, // 32 bytes Ed25519 public key
            privateKeyNacl: naclKeyPair.secretKey, // 64 bytes Ed25519 secret key (private + public for nacl)
            publicKeyBase58Nacl: KeyGen.convertToBase58(naclKeyPair.publicKey),
            secretKeyBase58Nacl: KeyGen.convertToBase58(naclKeyPair.secretKey)
        };
    }

    /**
     * Generates a new HD wallet with a fresh mnemonic and its corresponding master keys.
     * This is typically the starting point for a user's self-sovereign identity.
     * @returns {Promise<Object>} A promise resolving to the complete wallet object
     * containing the mnemonic, master node, and all derived key details.
     */
    static async generateHDWallet() {
        const seedPhrase = KeyGen.generateMnemonicSeed();
        const masterKeys = await KeyGen.generateHDMasterNode(seedPhrase);
        return {
            seedPhrase,
            ...masterKeys
        };
    }

    /**
     * Derives a child HD key from a parent node at a specific index.
     * This allows for creating multiple addresses/keys from a single seed,
     * maintaining a hierarchical structure for organization and privacy.
     * The derived child's private key is then used to generate a nacl-compatible keypair.
     * @param {Object} parentNodeResult - The result object from generateHDMasterNode or deriveFromPath.
     * Must contain a 'node' property (HDKey instance).
     * @param {number} index - The child index (0 to 2^31 - 1 for normal, +HDKey.HARDENED_OFFSET for hardened).
     * @param {boolean} [hardened=false] - Whether to use hardened derivation (isolated from parent public key).
     * @returns {Object} An object containing the derived child HD node and its key details,
     * including both BIP32 and nacl-compatible keypairs.
     * @throws {Error} If parentNodeResult is invalid or the derived child node's private key is null.
     */
    static deriveChildKey(parentNodeResult, index, hardened = false) {
        if (!parentNodeResult || !parentNodeResult.node) {
            throw new Error("Invalid parentNodeResult provided. Missing 'node' property.");
        }
        const parentNode = parentNodeResult.node;

        // Apply hardened offset if requested
        const derivationIndex = hardened ? index + HDKey.HARDENED_OFFSET : index;
        const childNode = parentNode.deriveChild(derivationIndex);

        if (!childNode.privateKey) {
            throw new Error("Derived child node private key is null. Cannot derive Ed25519 keypair.");
        }
        // Derive an Ed25519 keypair from the child node's private key
        const naclKeyPair = nacl.sign.keyPair.fromSeed(childNode.privateKey);


        return {
            node: childNode,
            // BIP32 key properties
            publicKeyBip32: childNode.publicKey,
            privateKeyBip32: childNode.privateKey,
            publicKeyBase58Bip32: KeyGen.convertToBase58(childNode.publicKey),
            privateKeyBase58Bip32: childNode.privateKey ? KeyGen.convertToBase58(childNode.privateKey) : null,
            chainCode: childNode.chainCode,
            chainCodeBase58: KeyGen.convertToBase58(childNode.chainCode),
            fingerprint: childNode.fingerprint,
            index: childNode.index,
            depth: childNode.depth,
            parentFingerprint: childNode.parentFingerprint,
            path: (hardened ? `${index}'` : `${index}`), // Path segment for clarity
            // Ed25519 key properties for tweetnacl compatibility
            publicKeyNacl: naclKeyPair.publicKey,
            privateKeyNacl: naclKeyPair.secretKey,
            publicKeyBase58Nacl: KeyGen.convertToBase58(naclKeyPair.publicKey),
            secretKeyBase58Nacl: KeyGen.convertToBase58(naclKeyPair.secretKey)
        };
    }

    /**
     * Derives an HD key directly using a derivation path string (e.g., "m/44'/0'/0'/0/0").
     * This provides a convenient way to navigate the HD key tree.
     * The derived node's private key is then used to generate a nacl-compatible keypair.
     * @param {Object} masterNodeResult - The result object from generateHDMasterNode.
     * Must contain a 'node' property (HDKey instance).
     * @param {string} path - The BIP-32/BIP-44 derivation path.
     * @returns {Object} An object containing the derived HD node and its key details,
     * including both BIP32 and nacl-compatible keypairs.
     * @throws {Error} If masterNodeResult is invalid or the derived node's private key is null.
     */
    static deriveFromPath(masterNodeResult, path) {
        if (!masterNodeResult || !masterNodeResult.node) {
            throw new Error("Invalid masterNodeResult provided. Missing 'node' property.");
        }
        const masterNode = masterNodeResult.node;

        const derivedNode = masterNode.derive(path);

        if (!derivedNode.privateKey) {
            throw new Error("Derived node private key is null. Cannot derive Ed25519 keypair.");
        }
        // Derive an Ed25519 keypair from the derived node's private key
        const naclKeyPair = nacl.sign.keyPair.fromSeed(derivedNode.privateKey);


        return {
            node: derivedNode,
            // BIP32 key properties
            publicKeyBip32: derivedNode.publicKey,
            privateKeyBip32: derivedNode.privateKey,
            publicKeyBase58Bip32: KeyGen.convertToBase58(derivedNode.publicKey),
            privateKeyBase58Bip32: derivedNode.privateKey ? KeyGen.convertToBase58(derivedNode.privateKey) : null,
            chainCode: derivedNode.chainCode,
            chainCodeBase58: KeyGen.convertToBase58(derivedNode.chainCode),
            fingerprint: derivedNode.fingerprint,
            index: derivedNode.index,
            depth: derivedNode.depth,
            parentFingerprint: derivedNode.parentFingerprint,
            path, // The derivation path used
            // Ed25519 key properties for tweetnacl compatibility
            publicKeyNacl: naclKeyPair.publicKey,
            privateKeyNacl: naclKeyPair.secretKey,
            publicKeyBase58Nacl: KeyGen.convertToBase58(naclKeyPair.publicKey),
            secretKeyBase58Nacl: KeyGen.convertToBase58(naclKeyPair.secretKey)
        };
    }

    /**
     * Creates an HD node from an extended key string (xpub or xprv).
     * This is useful for importing existing HD keys or sharing public keys
     * without exposing private keys.
     * If an xprv is provided, it also derives the corresponding nacl-compatible keypair.
     * @param {string} extendedKey - The extended key in base58 format (xpub or xprv).
     * @returns {Object} An object containing the HD node and its key details.
     */
    static fromExtendedKey(extendedKey) {
        const node = HDKey.fromExtendedKey(extendedKey);

        let naclKeyPair = null;
        if (node.privateKey) {
            // Only derive nacl keypair if a private key is present (i.e., not an xpub)
            naclKeyPair = nacl.sign.keyPair.fromSeed(node.privateKey);
        }

        return {
            node,
            // BIP32 key properties
            publicKeyBip32: node.publicKey,
            privateKeyBip32: node.privateKey,
            publicKeyBase58Bip32: KeyGen.convertToBase58(node.publicKey),
            privateKeyBase58Bip32: node.privateKey ? KeyGen.convertToBase58(node.privateKey) : null,
            chainCode: node.chainCode,
            chainCodeBase58: KeyGen.convertToBase58(node.chainCode),
            fingerprint: node.fingerprint,
            index: node.index,
            depth: node.depth,
            parentFingerprint: node.parentFingerprint,
            // Ed25519 key properties for tweetnacl compatibility (if private key available)
            publicKeyNacl: naclKeyPair ? naclKeyPair.publicKey : null,
            privateKeyNacl: naclKeyPair ? naclKeyPair.secretKey : null,
            publicKeyBase58Nacl: naclKeyPair ? KeyGen.convertToBase58(naclKeyPair.publicKey) : null,
            secretKeyBase58Nacl: naclKeyPair ? KeyGen.convertToBase58(naclKeyPair.secretKey) : null
        };
    }

    /**
     * Get the extended public key (xpub) for a node.
     * An xpub can be shared to derive public child keys without exposing the private key.
     * @param {Object} nodeResult - The result object from a derivation function (e.g., generateHDMasterNode).
     * Must contain a 'node' property (HDKey instance).
     * @returns {string} The extended public key.
     * @throws {Error} If nodeResult is invalid.
     */
    static getExtendedPublicKey(nodeResult) {
        if (!nodeResult || !nodeResult.node) {
            throw new Error("Invalid nodeResult provided. Missing 'node' property.");
        }
        return nodeResult.node.publicExtendedKey;
    }

    /**
     * Get the extended private key (xprv) for a node.
     * An xprv is the full master key for a branch of the HD tree.
     * @param {Object} nodeResult - The result object from a derivation function.
     * Must contain a 'node' property (HDKey instance).
     * @returns {string|null} The extended private key or null if the node is neutered (public only).
     * @throws {Error} If nodeResult is invalid.
     */
    static getExtendedPrivateKey(nodeResult) {
        if (!nodeResult || !nodeResult.node) {
            throw new Error("Invalid nodeResult provided. Missing 'node' property.");
        }
        return nodeResult.node.privateExtendedKey;
    }
}

// Named export for the class
export { KeyGen };
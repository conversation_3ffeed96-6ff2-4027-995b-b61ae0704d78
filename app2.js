import express from 'express';
import bodyParser from 'body-parser';
import mongoose from 'mongoose';
import jwt from 'jsonwebtoken';
import crypto from 'crypto'; // For hashing, particularly for proposalHash
import cors from 'cors';
import dotenv from 'dotenv';
import { KeyGen } from './utils/KeyGen.js'; // Updated path to utils directory
import User from './models/User.js'; // Your User model
import DAOConfig from './models/DAOConfig.js'; // Your DAOConfig model
import GovernanceProposal from './models/GovernanceProposal.js'; // Your GovernanceProposal model
import Transaction from './models/Transaction.js'; // Transaction audit model

// Load environment variables
dotenv.config();

const app = express();

// Middleware setup
app.use(cors({
    origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000', 'http://localhost:3001'],
    credentials: true
}));
app.use(bodyParser.json());

// --- Mongoose Connection ---
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/digital_commonwealth_2';
mongoose.connect(MONGODB_URI)
    .then(() => console.log('MongoDB connected successfully!'))
    .catch(err => {
        console.error('MongoDB connection error:', err);
        console.log('Continuing without MongoDB connection...');
    });

// --- JWT Secret (for session tokens) ---
const JWT_SECRET = process.env.JWT_SECRET || 'your_super_secret_jwt_key_for_digital_commonwealth';

// --- Global DAO Config Cache & Loader ---
// We'll load the DAOConfig once on startup and keep it in memory for quick access.
// This simulates fetching immutable blockchain state.
let globalDaoConfig = null;

const loadDAOConfig = async () => {
    try {
        globalDaoConfig = await DAOConfig.findById('global_dao_config');
        if (globalDaoConfig) {
            console.log('DAOConfig loaded successfully.');
        } else {
            console.warn('DAOConfig document not found. It needs to be initialized via "npm run init-dao" once.');
        }
    } catch (error) {
        console.error("Failed to load DAOConfig on startup:", error);
    }
};
loadDAOConfig(); // Call once on application start

// --- Middleware for authenticating requests ---
// Extracts publicKey from JWT token, attaches to req.user
const authenticateToken = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) return res.status(401).json({ message: "No authentication token provided." });

    jwt.verify(token, JWT_SECRET, (err, userPayload) => {
        if (err) return res.status(403).json({ message: "Invalid or expired authentication token." });
        // userPayload should contain { publicKey, username }
        req.user = userPayload; 
        next();
    });
};

// --- Middleware to check if the authenticated user is the DAO owner ---
// Requires authenticateToken to run before it.
const authorizeDAOOwner = async (req, res, next) => {
    if (!globalDaoConfig) {
        return res.status(503).json({ message: "DAO configuration not initialized. Please try again later." });
    }
    if (req.user.publicKey !== globalDaoConfig.ownerPublicKey) {
        return res.status(403).json({ message: "Unauthorized. Only the DAO owner can perform this action." });
    }
    next();
};

// --- Middleware to check if user holds minimum tokens (Commonwealth Coin) ---
// This middleware explicitly uses the User model to check reputationScore.
const checkMinTokens = async (req, res, next) => {
    try {
        const currentUser = await User.findById(req.user.publicKey); // Corrected to findById
        if (!currentUser) {
            return res.status(404).json({ message: "Authenticated user not found." });
        }
        const daoConfig = globalDaoConfig; // Use the cached globalDaoConfig
        if (!daoConfig) {
             console.error('DAOConfig not loaded in cache. This should not happen if initialization is successful.');
             return res.status(503).json({ message: "Server configuration error: DAOConfig not available." });
        }
        if (currentUser.reputationScore < daoConfig.minProposalCreationTokens) {
            return res.status(403).json({ message: `Insufficient tokens. Requires at least ${daoConfig.minProposalCreationTokens} ${daoConfig.nativeCoinSymbol}.` });
        }
        next();
    } catch (error) {
        console.error("Error checking token balance in checkMinTokens middleware:", error);
        res.status(500).json({ message: "Internal server error during token balance check." });
    }
};

// --- API ROUTES FOR USER IDENTITY ---

// POST /api/identity/register
// Registers a new user with their public key and username
app.post('/api/identity/register', async (req, res) => {
    const { username, publicKey, profile } = req.body;

    if (!username || !publicKey) {
        return res.status(400).json({ message: "Username and public key are required." });
    }

    try {
        // Check if user already exists using publicKey as _id
        const existingUser = await User.findById(publicKey); // Corrected to findById
        if (existingUser) {
            return res.status(409).json({ message: "User with this public key already exists.", user: existingUser });
        }

        // Check if username is taken
        const existingUsername = await User.findOne({ username });
        if (existingUsername) {
            return res.status(409).json({ message: "Username is already taken." });
        }

        // Create new user (User model's pre-save hook will set _id to publicKey)
        const newUser = new User({
            _id: publicKey, // Explicitly set _id to publicKey as per your schema
            username,
            reputationScore: 0, // Start with 0 tokens
            profile: profile || {}
        });

        await newUser.save();
        console.log(`Event: UserRegistered - Username: ${username}, PublicKey: ${publicKey}`);

        res.status(201).json({
            message: "User registered successfully!",
            user: {
                username: newUser.username,
                publicKey: newUser._id, // Refer to _id as publicKey in response
                reputationScore: newUser.reputationScore,
                profile: newUser.profile
            }
        });
    } catch (error) {
        console.error("Error registering user:", error);
        res.status(500).json({ message: "Server error during user registration." });
    }
});

// POST /api/identity/login
// Authenticates a user and returns a JWT token
app.post('/api/identity/login', async (req, res) => {
    const { publicKey, signature, message } = req.body;

    if (!publicKey || !signature || !message) {
        return res.status(400).json({ message: "Public key, signature, and message are required." });
    }

    try {
        // Verify the signature
        const isSignatureValid = KeyGen.verifySignature(message, signature, publicKey);
        if (!isSignatureValid) {
            return res.status(401).json({ message: "Invalid signature. Authentication failed." });
        }

        // Find the user by publicKey (which is _id)
        const user = await User.findById(publicKey); // Corrected to findById
        if (!user) {
            return res.status(404).json({ message: "User not found. Please register first." });
        }

        if (!user.isActive) {
            return res.status(403).json({ message: "User account is deactivated." });
        }

        user.lastLogin = new Date();
        await user.save();

        const token = jwt.sign(
            {
                publicKey: user._id, // Use _id as publicKey in token payload
                username: user.username,
                roles: user.roles
            },
            JWT_SECRET,
            { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
        );

        console.log(`Event: UserLogin - Username: ${user.username}, PublicKey: ${publicKey}`);

        res.json({
            message: "Login successful!",
            token,
            user: {
                username: user.username,
                publicKey: user._id, // Refer to _id as publicKey
                reputationScore: user.reputationScore,
                profile: user.profile,
                roles: user.roles
            }
        });
    } catch (error) {
        console.error("Error during login:", error);
        res.status(500).json({ message: "Server error during authentication." });
    }
});

// GET /api/identity/profile/:publicKey
// Gets a user's profile information
app.get('/api/identity/profile/:publicKey', async (req, res) => {
    const { publicKey } = req.params;

    try {
        const user = await User.findById(publicKey); // Corrected to findById
        if (!user) {
            return res.status(404).json({ message: "User not found." });
        }

        res.json({
            user: {
                username: user.username,
                publicKey: user._id, // Refer to _id as publicKey
                reputationScore: user.reputationScore,
                profile: user.profile,
                governanceStats: user.governanceStats,
                isVerified: user.isVerified,
                roles: user.roles,
                createdAt: user.createdAt
            }
        });
    } catch (error) {
        console.error("Error fetching user profile:", error);
        res.status(500).json({ message: "Server error fetching user profile." });
    }
});

// PUT /api/identity/profile
// Updates the authenticated user's profile
app.put('/api/identity/profile', authenticateToken, async (req, res) => {
    const { profile } = req.body;

    try {
        const user = await User.findById(req.user.publicKey); // Corrected to findById
        if (!user) {
            return res.status(404).json({ message: "User not found." });
        }

        if (profile) {
            // Merge existing profile with new profile data
            user.profile = { ...user.profile, ...profile };
        }

        await user.save();

        res.json({
            message: "Profile updated successfully!",
            user: {
                username: user.username,
                publicKey: user._id, // Refer to _id as publicKey
                profile: user.profile
            }
        });
    } catch (error) {
        console.error("Error updating profile:", error);
        res.status(500).json({ message: "Server error updating profile." });
    }
});

// --- API ROUTES FOR DAOConfig ---

// POST /api/dao-config/initialize
// Initializes the global DAO configuration. Can only be called once.
// This endpoint only creates the DAOConfig document. Genesis token distribution
// is handled by the 'initialize-dao.js' script.
app.post('/api/dao-config/initialize', async (req, res) => {
    // Prevent re-initialization if config already exists
    if (globalDaoConfig) {
        return res.status(409).json({ message: "DAO configuration already initialized." });
    }

    const {
        ownerPublicKey,
        nativeCoinName,
        nativeCoinSymbol,
        totalSupply,
        initialDistributionStrategy,
        genesisAllocations,
        votingDuration,
        minimumQuorumPercentage,
        minProposalCreationTokens,
        // clientProvidedSignature and messageSignedByOwner are crucial for verification
        clientProvidedSignature,
        messageSignedByOwner // The stringified payload that the owner signed
    } = req.body;

    // Basic validation
    if (!ownerPublicKey || !clientProvidedSignature || !messageSignedByOwner || !nativeCoinName || !nativeCoinSymbol || totalSupply == null || votingDuration == null || minimumQuorumPercentage == null || minProposalCreationTokens == null) {
        return res.status(400).json({ message: "Missing required DAO configuration parameters or signature details." });
    }

    try {
        // 1. Verify the owner's signature over the entire payload
        // This is a critical step: the owner must sign the exact configuration they intend to initialize.
        const isSignatureValid = KeyGen.verifySignature(messageSignedByOwner, clientProvidedSignature, ownerPublicKey);
        if (!isSignatureValid) {
            return res.status(401).json({ message: "Invalid owner signature for initialization." });
        }

        const newConfig = new DAOConfig({
            _id: 'global_dao_config', // Enforces single document
            ownerPublicKey,
            nativeCoinName,
            nativeCoinSymbol,
            totalSupply,
            initialDistributionStrategy,
            genesisAllocations: genesisAllocations || [], // Ensure it's an array
            votingDuration,
            minimumQuorumPercentage,
            minProposalCreationTokens
        });

        const savedConfig = await newConfig.save();
        globalDaoConfig = savedConfig; // Update in-memory cache

        console.log(`Event: DAOConfigInitialized - Owner: ${ownerPublicKey}, Total Supply: ${totalSupply}`);

        res.status(201).json({ message: "DAO configuration initialized successfully.", config: savedConfig });

    } catch (error) {
        // Handle cases where _id 'global_dao_config' already exists (due to unique:true)
        if (error.code === 11000) { // MongoDB duplicate key error
            return res.status(409).json({ message: "DAO configuration has already been initialized." });
        }
        console.error("Error initializing DAO config:", error);
        res.status(500).json({ message: "Server error during DAO configuration initialization." });
    }
});

// GET /api/dao-config
// Retrieves the current global DAO configuration. Publicly accessible.
app.get('/api/dao-config', (req, res) => {
    if (!globalDaoConfig) {
        return res.status(404).json({ message: "DAO configuration not found. It needs to be initialized." });
    }
    res.json({ config: globalDaoConfig });
});

// PUT /api/dao-config/update
// Updates the global DAO configuration. Only accessible by the ownerPublicKey
// and requires a signed message from the owner.
app.put('/api/dao-config/update', authenticateToken, authorizeDAOOwner, async (req, res) => {
    if (!globalDaoConfig) {
        return res.status(404).json({ message: "DAO configuration not found. It needs to be initialized." });
    }

    const {
        // Parameters allowed to be updated (e.g., votingDuration, minProposalCreationTokens, quorum)
        // ownerPublicKey should NOT be updated via this route, if ever, it requires a separate governance proposal.
        votingDuration,
        minimumQuorumPercentage,
        minProposalCreationTokens,
        // Add other updatable fields as needed.
        // And the signature for the update payload
        clientProvidedSignature,
        messageSignedByOwner // The stringified payload that the owner signed
    } = req.body;

    if (!clientProvidedSignature || !messageSignedByOwner) {
        return res.status(400).json({ message: "Missing owner signature for update." });
    }

    try {
        // Construct the message that was signed by the client for verification
        // This should be the exact subset of fields being updated, stringified.
        const updatePayloadToVerify = JSON.stringify({
            votingDuration,
            minimumQuorumPercentage,
            minProposalCreationTokens,
            // ... include any other fields that are being updated
        });

        const isSignatureValid = KeyGen.verifySignature(updatePayloadToVerify, clientProvidedSignature, req.user.publicKey);
        if (!isSignatureValid) {
            return res.status(401).json({ message: "Invalid owner signature for update." });
        }

        // Apply updates
        if (votingDuration != null) globalDaoConfig.votingDuration = votingDuration;
        if (minimumQuorumPercentage != null) globalDaoConfig.minimumQuorumPercentage = minimumQuorumPercentage;
        if (minProposalCreationTokens != null) globalDaoConfig.minProposalCreationTokens = minProposalCreationTokens;
        // ... apply other updates similarly

        const updatedConfig = await globalDaoConfig.save();
        // globalDaoConfig is already updated by reference due to Mongoose save

        console.log(`Event: DAOConfigUpdated - By: ${req.user.publicKey}`);
        res.json({ message: "DAO configuration updated successfully.", config: updatedConfig });

    } catch (error) {
        console.error("Error updating DAO config:", error);
        res.status(500).json({ message: "Server error during DAO configuration update." });
    }
});

// --- API ROUTES FOR TOKEN MANAGEMENT ---

// POST /api/tokens/mint
// Mints new tokens and distributes them to specified users (DAO owner only)
app.post('/api/tokens/mint', authenticateToken, authorizeDAOOwner, async (req, res) => {
    const { recipients, reason, clientProvidedSignature, messageSignedByOwner } = req.body;

    if (!recipients || !Array.isArray(recipients) || recipients.length === 0) {
        return res.status(400).json({ message: "Recipients array is required and must not be empty." });
    }

    if (!clientProvidedSignature || !messageSignedByOwner) {
        return res.status(400).json({ message: "Owner signature is required for token minting." });
    }

    try {
        // Verify the owner's signature over the minting request
        const isSignatureValid = KeyGen.verifySignature(messageSignedByOwner, clientProvidedSignature, req.user.publicKey);
        if (!isSignatureValid) {
            return res.status(401).json({ message: "Invalid owner signature for token minting." });
        }

        const results = [];
        let totalMinted = 0;
        const daoConfig = globalDaoConfig; // Use cached config

        for (const recipient of recipients) {
            const { publicKey, amount } = recipient;

            if (!publicKey || amount == null || amount <= 0) {
                results.push({ publicKey, success: false, error: "Invalid recipient data (missing pubkey or invalid amount)" });
                continue;
            }

            try {
                const user = await User.findById(publicKey); // Corrected to findById
                if (!user) {
                    results.push({ publicKey, success: false, error: "Recipient user not found" });
                    continue;
                }

                // Record balances before transaction for audit
                const oldBalance = user.reputationScore;
                user.reputationScore += amount;
                await user.save();

                // Record the mint transaction
                await Transaction.recordTransaction({
                    type: 'admin_mint',
                    amount: amount,
                    from: { publicKey: null, username: 'SYSTEM' }, // Minting from the system
                    to: { publicKey: user._id, username: user.username },
                    memo: reason || `Minted by DAO owner for ${user.username}`,
                    reason: 'Owner-initiated minting',
                    signature: clientProvidedSignature, // Owner's signature for the mint
                    signedMessage: messageSignedByOwner,
                    status: 'completed',
                    balances: {
                        fromBefore: null, fromAfter: null, // No 'from' account for minting
                        toBefore: oldBalance,
                        toAfter: user.reputationScore
                    },
                    executedBy: { publicKey: req.user.publicKey, username: req.user.username }
                });

                totalMinted += amount;
                results.push({
                    publicKey,
                    username: user.username,
                    success: true,
                    amount,
                    oldBalance,
                    newBalance: user.reputationScore
                });

                console.log(`Event: TokensMinted - User: ${user.username}, Amount: ${amount}, Reason: ${reason || 'Admin mint'}`);

            } catch (userError) {
                console.error(`Error minting tokens for ${publicKey}:`, userError);
                results.push({ publicKey, success: false, error: `Database error: ${userError.message}` });
            }
        }
        
        // Update total supply in DAOConfig if this minting genuinely increases the overall supply
        // This is a simplified model. In a real blockchain, the token contract would manage total supply.
        // Here, we explicitly update DAOConfig's totalSupply.
        globalDaoConfig.totalSupply += totalMinted;
        await globalDaoConfig.save();


        console.log(`Token minting complete: ${totalMinted} ${daoConfig.nativeCoinSymbol} minted to ${results.filter(r => r.success).length} users`);

        res.json({
            message: "Token minting completed.",
            totalMinted,
            coinSymbol: daoConfig.nativeCoinSymbol,
            results,
            reason: reason || 'Admin mint',
            newTotalSupply: daoConfig.totalSupply // Show updated total supply
        });

    } catch (error) {
        console.error("Error during token minting:", error);
        res.status(500).json({ message: "Server error during token minting." });
    }
});

// POST /api/tokens/distribute
// Distributes tokens from the DAO owner's account/treasury to users (DAO owner only)
app.post('/api/tokens/distribute', authenticateToken, authorizeDAOOwner, async (req, res) => {
    const { toPublicKey, amount, memo, clientProvidedSignature, messageSignedByOwner } = req.body;
    const distributorPublicKey = req.user.publicKey; // Authenticated owner

    if (!toPublicKey || amount == null || amount <= 0) {
        return res.status(400).json({ message: "Valid recipient public key and positive amount are required." });
    }

    if (!clientProvidedSignature || !messageSignedByOwner) {
        return res.status(400).json({ message: "Owner signature is required for token distribution." });
    }

    try {
        // Verify the owner's signature
        const isSignatureValid = KeyGen.verifySignature(messageSignedByOwner, clientProvidedSignature, distributorPublicKey);
        if (!isSignatureValid) {
            return res.status(401).json({ message: "Invalid owner signature for token distribution." });
        }

        const distributorUser = await User.findById(distributorPublicKey); // Corrected to findById
        const recipientUser = await User.findById(toPublicKey); // Corrected to findById

        if (!distributorUser) {
            return res.status(404).json({ message: "Distributor user (DAO owner) not found." });
        }
        if (!recipientUser) {
            return res.status(404).json({ message: "Recipient user not found." });
        }
        if (distributorUser.reputationScore < amount) {
            return res.status(403).json({ message: "Insufficient balance in distributor's account for distribution." });
        }
        if (distributorPublicKey === toPublicKey) {
            return res.status(400).json({ message: "Cannot distribute tokens to yourself from your own balance via this route. Use /transfer if sending to self." });
        }

        const distributorBalanceBefore = distributorUser.reputationScore;
        const recipientBalanceBefore = recipientUser.reputationScore;

        distributorUser.reputationScore -= amount;
        recipientUser.reputationScore += amount;

        await distributorUser.save();
        await recipientUser.save();

        await Transaction.recordTransaction({
            type: 'admin_distribution', // Type for distribution from owner/treasury
            amount,
            from: { publicKey: distributorPublicKey, username: distributorUser.username },
            to: { publicKey: toPublicKey, username: recipientUser.username },
            memo: memo || `Distribution from DAO owner to ${recipientUser.username}`,
            reason: 'Owner-initiated distribution from personal balance/treasury',
            signature: clientProvidedSignature,
            signedMessage: messageSignedByOwner,
            status: 'completed',
            balances: {
                fromBefore: distributorBalanceBefore,
                fromAfter: distributorUser.reputationScore,
                toBefore: recipientBalanceBefore,
                toAfter: recipientUser.reputationScore
            },
            executedBy: { publicKey: distributorPublicKey, username: distributorUser.username }
        });

        console.log(`Event: TokensDistributed - From: ${distributorPublicKey}, To: ${toPublicKey}, Amount: ${amount}`);
        res.json({ message: "Tokens distributed successfully!", distributor: { publicKey: distributorUser._id, newBalance: distributorUser.reputationScore }, recipient: { publicKey: recipientUser._id, newBalance: recipientUser.reputationScore } });

    } catch (error) {
        console.error("Error during token distribution:", error);
        res.status(500).json({ message: error.message || "Server error during token distribution." });
    }
});


// GET /api/tokens/balance/:publicKey
// Gets the token balance for a specific user
app.get('/api/tokens/balance/:publicKey', async (req, res) => {
    const { publicKey } = req.params;

    try {
        const user = await User.findById(publicKey); // Corrected to findById
        if (!user) {
            return res.status(404).json({ message: "User not found." });
        }

        res.json({
            user: {
                username: user.username,
                publicKey: user._id, // Refer to _id as publicKey
                balance: user.reputationScore,
                coinSymbol: globalDaoConfig?.nativeCoinSymbol || 'CMC'
            }
        });

    }
    catch (error) {
        console.error("Error fetching token balance:", error);
        res.status(500).json({ message: "Server error fetching token balance." });
    }
});

// GET /api/tokens/supply
// Gets information about the total token supply and distribution
app.get('/api/tokens/supply', async (req, res) => {
    try {
        if (!globalDaoConfig) {
            return res.status(404).json({ message: "DAO configuration not found." });
        }

        // Calculate circulating supply by summing all user balances
        const users = await User.find({}, 'reputationScore _id username profile.displayName'); // Include _id, username for topHolders
        const circulatingSupply = users.reduce((total, user) => total + user.reputationScore, 0);

        // Get top holders
        const topHolders = users
            .filter(user => user.reputationScore > 0)
            .sort((a, b) => b.reputationScore - a.reputationScore)
            .slice(0, 10) // Limit to top 10
            .map(user => ({
                username: user.username,
                publicKey: user._id, // Refer to _id as publicKey
                balance: user.reputationScore,
                percentage: ((user.reputationScore / circulatingSupply) * 100).toFixed(2)
            }));

        res.json({
            tokenInfo: {
                name: globalDaoConfig.nativeCoinName,
                symbol: globalDaoConfig.nativeCoinSymbol,
                totalSupply: globalDaoConfig.totalSupply,
                circulatingSupply,
                remainingSupply: globalDaoConfig.totalSupply - circulatingSupply
            },
            distribution: {
                totalHolders: users.filter(u => u.reputationScore > 0).length,
                topHolders: topHolders
            }
        });

    } catch (error) {
        console.error("Error fetching token supply info:", error);
        res.status(500).json({ message: "Server error fetching token supply information." });
    }
});

// POST /api/tokens/transfer
// Transfers tokens between users with cryptographic signature verification
app.post('/api/tokens/transfer', authenticateToken, async (req, res) => {
    const { toPublicKey, amount, memo, clientProvidedSignature, messageSignedForTransfer } = req.body;
    const fromPublicKey = req.user.publicKey; // Sender is the authenticated user

    if (!toPublicKey || amount == null || amount <= 0) {
        return res.status(400).json({ message: "Valid recipient public key and positive amount are required." });
    }

    if (!clientProvidedSignature || !messageSignedForTransfer) {
        return res.status(400).json({ message: "Sender signature and signed message are required for token transfer." });
    }

    if (fromPublicKey === toPublicKey) {
        return res.status(400).json({ message: "Cannot transfer tokens to yourself." });
    }

    try {
        // Verify the sender's signature over the transfer details
        const isSignatureValid = KeyGen.verifySignature(messageSignedForTransfer, clientProvidedSignature, fromPublicKey);
        if (!isSignatureValid) {
            return res.status(401).json({ message: "Invalid sender signature for transfer." });
        }

        // Find sender and recipient users
        const [sender, recipient] = await Promise.all([
            User.findById(fromPublicKey), // Corrected to findById
            User.findById(toPublicKey) // Corrected to findById
        ]);

        if (!sender) {
            return res.status(404).json({ message: "Sender user not found." });
        }

        if (!recipient) {
            return res.status(404).json({ message: "Recipient user not found." });
        }

        // Check if sender has sufficient balance
        if (sender.reputationScore < amount) {
            return res.status(400).json({
                message: "Insufficient balance for transfer.",
                currentBalance: sender.reputationScore,
                requestedAmount: amount
            });
        }

        // Perform the transfer
        const senderOldBalance = sender.reputationScore;
        const recipientOldBalance = recipient.reputationScore;

        sender.reputationScore -= amount;
        recipient.reputationScore += amount;

        // Save both users
        await Promise.all([sender.save(), recipient.save()]);

        // Record the transaction for audit purposes
        const newTransaction = await Transaction.recordTransaction({
            type: 'user_transfer',
            amount,
            from: {
                publicKey: fromPublicKey,
                username: sender.username
            },
            to: {
                publicKey: toPublicKey,
                username: recipient.username
            },
            memo: memo || '',
            reason: 'User-to-user transfer',
            signature: clientProvidedSignature,
            signedMessage: messageSignedForTransfer, // Store the exact signed message
            balances: {
                fromBefore: senderOldBalance,
                fromAfter: sender.reputationScore,
                toBefore: recipientOldBalance,
                toAfter: recipient.reputationScore
            },
            executedBy: { // The user who executed the transfer (the sender)
                publicKey: sender._id,
                username: sender.username
            }
        });

        console.log(`Event: TokensTransferred - From: ${sender.username} (${fromPublicKey}), To: ${recipient.username} (${toPublicKey}), Amount: ${amount}`);

        res.json({
            message: "Token transfer completed successfully.",
            transfer: {
                from: {
                    username: sender.username,
                    publicKey: sender._id,
                    oldBalance: senderOldBalance,
                    newBalance: sender.reputationScore
                },
                to: {
                    username: recipient.username,
                    publicKey: recipient._id,
                    oldBalance: recipientOldBalance,
                    newBalance: recipient.reputationScore
                },
                amount,
                memo: memo || '',
                coinSymbol: globalDaoConfig?.nativeCoinSymbol || 'CMC',
                timestamp: new Date().toISOString(),
                transactionId: newTransaction._id
            }
        });

    } catch (error) {
        console.error("Error during token transfer:", error);
        res.status(500).json({ message: error.message || "Server error during token transfer." });
    }
});

// GET /api/tokens/transfers/:publicKey
// Gets transfer history for a specific user
app.get('/api/tokens/transfers/:publicKey', async (req, res) => {
    const { publicKey } = req.params;
    const { limit = 50, offset = 0, type = null } = req.query;

    try {
        const user = await User.findById(publicKey); // Corrected to findById
        if (!user) {
            return res.status(404).json({ message: "User not found." });
        }

        // Get transaction history using the Transaction model
        const { transactions, total } = await Transaction.getUserTransactions(publicKey, {
            limit,
            offset,
            type
        });

        // Format transactions for response
        const formattedTransactions = transactions.map(tx => ({
            id: tx._id,
            type: tx.type,
            amount: tx.amount,
            from: tx.from,
            to: tx.to,
            memo: tx.memo,
            reason: tx.reason,
            status: tx.status,
            timestamp: tx.createdAt,
            balances: tx.balances,
            signature: tx.signature, // Include signature in response
            signedMessage: tx.signedMessage, // Include signed message in response
            executedBy: tx.executedBy, // Include who executed
            // Determine if this user was sender or receiver
            direction: tx.from.publicKey === publicKey ? 'sent' : 'received'
        }));

        res.json({
            user: {
                username: user.username,
                publicKey: user._id, // Refer to _id as publicKey
                currentBalance: user.reputationScore
            },
            transactions: formattedTransactions,
            pagination: {
                limit: parseInt(limit),
                offset: parseInt(offset),
                total
            },
            coinSymbol: globalDaoConfig?.nativeCoinSymbol || 'CMC'
        });

    } catch (error) {
        console.error("Error fetching transfer history:", error);
        res.status(500).json({ message: "Server error fetching transfer history." });
    }
});

// GET /api/tokens/transactions/stats
// Gets overall transaction statistics
app.get('/api/tokens/transactions/stats', async (req, res) => {
    try {
        const stats = await Transaction.getTransactionStats();

        res.json({
            statistics: stats,
            coinSymbol: globalDaoConfig?.nativeCoinSymbol || 'CMC'
        });

    } catch (error) {
        console.error("Error fetching transaction statistics:", error);
        res.status(500).json({ message: "Server error fetching transaction statistics." });
    }
});


// --- API ROUTES FOR GOVERNANCE ---

// POST /api/governance/proposals - Create a new proposal
app.post('/api/governance/proposals', authenticateToken, checkMinTokens, async (req, res) => {
    const { title, description, clientProvidedAuthorSignature, messageSignedByAuthor, category, relatedEntityId } = req.body;
    const authorPublicKey = req.user.publicKey; 

    if (!title || !description || !clientProvidedAuthorSignature || !messageSignedByAuthor) {
        return res.status(400).json({ message: "Title, description, author signature, and signed message are required." });
    }

    try {
        const isAuthorSignatureValid = KeyGen.verifySignature(messageSignedByAuthor, clientProvidedAuthorSignature, authorPublicKey);
        if (!isAuthorSignatureValid) {
            return res.status(401).json({ message: "Invalid author signature. Proposal creation failed." });
        }

        const contentToHash = title + description;
        const msgUint8 = KeyGen.encodeUTF8(contentToHash);
        const proposalHash = crypto.createHash('sha256').update(msgUint8).digest('base64');

        const existingProposal = await GovernanceProposal.findOne({ proposalHash });
        if (existingProposal) {
            return res.status(409).json({ message: "A proposal with this exact content already exists." });
        }

        const daoConfig = globalDaoConfig;
        if (!daoConfig) {
             console.error('DAOConfig not loaded. Please initialize it.');
             return res.status(500).json({ message: "Server configuration error: DAOConfig not available." });
        }

        const now = Math.floor(Date.now() / 1000); 
        const votingDurationSeconds = daoConfig.votingDuration;

        const newProposal = new GovernanceProposal({
            title,
            description,
            authorPublicKey,
            authorSignature: clientProvidedAuthorSignature,
            proposalHash,
            status: 'Active', 
            startTime: now,
            endTime: now + votingDurationSeconds,
            threshold: daoConfig.minimumQuorumPercentage, 
            category,
            relatedEntityId
        });

        await newProposal.save();

        // Update user's governance stats
        await User.updateOne(
            { _id: authorPublicKey }, // User's _id is their publicKey
            { $inc: { 'governanceStats.proposalsCreated': 1 } }
        );

        console.log(`Event: ProposalCreated - ID: ${newProposal._id}, Proposer: ${authorPublicKey}, Title: "${title}"`);

        res.status(201).json({ message: "Proposal created successfully!", proposal: newProposal });
    } catch (error) {
        console.error("Error creating proposal:", error);
        res.status(500).json({ message: error.message || "Server error creating proposal." });
    }
});

// POST /api/governance/proposals/:proposalId/vote - Cast a vote on a proposal
app.post('/api/governance/proposals/:proposalId/vote', authenticateToken, async (req, res) => {
    const { proposalId } = req.params;
    const { voteType, clientProvidedVoteSignature, messageSignedForVote } = req.body;
    const voterPublicKey = req.user.publicKey;

    if (!voteType || !clientProvidedVoteSignature || !messageSignedForVote) {
        return res.status(400).json({ message: "Vote type, signature, and message for vote are required." });
    }

    try {
        const proposal = await GovernanceProposal.findById(proposalId);
        if (!proposal) {
            return res.status(404).json({ message: "Proposal not found." });
        }

        const now = Math.floor(Date.now() / 1000);
        if (now < proposal.startTime || now > proposal.endTime) { // Check if within voting period
            // If voting period has ended, update status
            if (now > proposal.endTime && proposal.status === 'Active') {
                proposal.status = 'VotingEnded';
                await proposal.save();
            }
            return res.status(403).json({ message: "Voting is not active for this proposal or has ended." });
        }
        if (proposal.status !== 'Active') {
            return res.status(403).json({ message: `Proposal status is '${proposal.status}'. Cannot vote.` });
        }


        const voterUser = await User.findById(voterPublicKey); // Corrected to findById
        if (!voterUser || voterUser.reputationScore <= 0) {
            return res.status(403).json({ message: `You must hold ${globalDaoConfig?.nativeCoinSymbol || 'tokens'} to vote.` });
        }
        const voteWeight = voterUser.reputationScore;

        await proposal.addVote(voterPublicKey, voteType, voteWeight, clientProvidedVoteSignature, messageSignedForVote);

        // Update user's governance stats for vote cast
        await User.updateOne(
            { _id: voterPublicKey }, // User's _id is their publicKey
            {
                $inc: {
                    'governanceStats.votesCast': 1,
                    'governanceStats.totalVotingWeight': voteWeight
                }
            }
        );

        console.log(`Event: VoteCast - Voter: ${voterPublicKey}, Proposal: ${proposalId}, Type: ${voteType}, Weight: ${voteWeight}`);

        res.json({ message: "Vote cast successfully!", proposal });
    } catch (error) {
        console.error("Error casting vote:", error.message);
        res.status(500).json({ message: error.message || "Server error casting vote." });
    }
});

// POST /api/governance/proposals/:proposalId/execute - Execute a proposal
app.post('/api/governance/proposals/:proposalId/execute', authenticateToken, async (req, res) => {
    const { proposalId } = req.params;
    const executorPublicKey = req.user.publicKey;

    try {
        const proposal = await GovernanceProposal.findById(proposalId);
        if (!proposal) {
            return res.status(404).json({ message: "Proposal not found." });
        }

        const daoConfig = globalDaoConfig;
        if (!daoConfig) {
             console.error('DAOConfig not loaded. Please initialize it.');
             return res.status(500).json({ message: "Server configuration error: DAOConfig not available." });
        }

        // Only DAO owner can execute for now (can be changed to a governance vote later)
        if (daoConfig.ownerPublicKey !== executorPublicKey) {
            return res.status(403).json({ message: "Unauthorized. Only the DAO owner can execute proposals directly." });
        }

        const now = Math.floor(Date.now() / 1000);

        if (now < proposal.endTime) {
            return res.status(403).json({ message: "Voting period has not ended yet." });
        }
        if (proposal.totalWeightFor <= proposal.totalWeightAgainst) {
            return res.status(403).json({ message: "Proposal has not passed based on total vote weight." });
        }
        const totalPossibleVotes = daoConfig.totalSupply;
        const minimumQuorumWeight = totalPossibleVotes * daoConfig.minimumQuorumPercentage;
        const totalWeightCast = proposal.totalWeightFor + proposal.totalWeightAgainst; 
        if (totalWeightCast < minimumQuorumWeight) {
            return res.status(403).json({ message: `Quorum not reached. Need ${minimumQuorumWeight} vote weight, but only ${totalWeightCast} cast.` });
        }
        if (proposal.executed) {
            return res.status(403).json({ message: "Proposal already executed." });
        }

        // Mark as executed and update status
        proposal.executed = true;
        proposal.status = 'Executed';
        proposal.outcome = `Proposal to "${proposal.title}" executed by ${executorPublicKey}.`;

        await proposal.save();

        console.log(`EXECUTING PROPOSAL ACTION for ${proposalId}: "${proposal.title}"`);
        console.log(`Event: ProposalExecuted - ID: ${proposalId}`);

        res.json({ message: "Proposal executed successfully!", proposal });
    } catch (error) {
        console.error("Error executing proposal:", error.message);
        res.status(500).json({ message: error.message || "Server error executing proposal." });
    }
});

// GET /api/governance/proposals - Get all proposals
app.get('/api/governance/proposals', async (req, res) => {
    try {
        const proposals = await GovernanceProposal.find({}).sort({ createdAt: -1 });
        res.json({ proposals });
    } catch (error) {
        console.error("Error fetching proposals:", error);
        res.status(500).json({ message: "Server error fetching proposals." });
    }
});

// GET /api/governance/proposals/:proposalId - Get single proposal
app.get('/api/governance/proposals/:proposalId', async (req, res) => {
    try {
        const proposal = await GovernanceProposal.findById(req.params.proposalId);
        if (!proposal) {
            return res.status(404).json({ message: "Proposal not found." });
        }
        res.json({ proposal });
    } catch (error) {
        console.error("Error fetching proposal:", error);
        res.status(500).json({ message: "Server error fetching proposal." });
    }
});


// --- UTILITY/HEALTHCHECK ROUTES ---
app.get('/', (req, res) => {
    res.send('Digital Commonwealth Backend Running! Access APIs at /api/...');
});

app.get('/test', (req, res) => {
    res.json({ message: 'Server is running!' });
});


// Start the server
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
    console.log(`Digital Commonwealth API server listening on port ${PORT}`);
    console.log(`Test endpoint available at http://localhost:${PORT}/test`);
});

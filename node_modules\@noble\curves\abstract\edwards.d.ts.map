{"version": 3, "file": "edwards.d.ts", "sourceRoot": "", "sources": ["../src/abstract/edwards.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AACH,sEAAsE;AACtE,OAAO,EAYL,KAAK,KAAK,EACV,KAAK,GAAG,EACT,MAAM,aAAa,CAAC;AACrB,OAAO,EAKL,KAAK,WAAW,EAChB,KAAK,UAAU,EACf,KAAK,KAAK,EACV,KAAK,gBAAgB,EACtB,MAAM,YAAY,CAAC;AACpB,OAAO,EAAS,KAAK,MAAM,EAAE,KAAK,OAAO,EAAE,MAAM,cAAc,CAAC;AAMhE,MAAM,MAAM,OAAO,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,KAAK;IAAE,OAAO,EAAE,OAAO,CAAC;IAAC,KAAK,EAAE,MAAM,CAAA;CAAE,CAAC;AAEpF,gDAAgD;AAChD,MAAM,MAAM,SAAS,GAAG,UAAU,CAAC,MAAM,CAAC,GAAG;IAC3C,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;IACV,IAAI,EAAE,KAAK,CAAC;IACZ,WAAW,CAAC,EAAE,CAAC,WAAW,CAAC,EAAE,MAAM,KAAK,UAAU,CAAC;IACnD,iBAAiB,CAAC,EAAE,CAAC,KAAK,EAAE,UAAU,KAAK,UAAU,CAAC;IACtD,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,UAAU,EAAE,GAAG,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,KAAK,UAAU,CAAC;IAC5E,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB,OAAO,CAAC,EAAE,KAAK,CAAC;IAChB,UAAU,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,WAAW,CAAC,MAAM,CAAC,CAAC;CACxD,CAAC;AAEF,MAAM,MAAM,mBAAmB,GAAG,QAAQ,CAAC,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;AAKzE,iEAAiE;AACjE,MAAM,WAAW,YAAa,SAAQ,KAAK,CAAC,YAAY,CAAC;IACvD,QAAQ,CAAC,EAAE,EAAE,MAAM,CAAC;IACpB,QAAQ,CAAC,EAAE,EAAE,MAAM,CAAC;IACpB,QAAQ,CAAC,EAAE,EAAE,MAAM,CAAC;IACpB,QAAQ,CAAC,EAAE,EAAE,MAAM,CAAC;IACpB,IAAI,CAAC,IAAI,MAAM,CAAC;IAChB,IAAI,CAAC,IAAI,MAAM,CAAC;IAChB,cAAc,IAAI,IAAI,CAAC;IACvB,QAAQ,CAAC,MAAM,EAAE,MAAM,GAAG,YAAY,CAAC;IACvC,cAAc,CAAC,MAAM,EAAE,MAAM,GAAG,YAAY,CAAC;IAC7C,GAAG,IAAI,OAAO,CAAC;IACf,YAAY,IAAI,OAAO,CAAC;IACxB,aAAa,IAAI,OAAO,CAAC;IACzB,aAAa,IAAI,YAAY,CAAC;IAC9B,QAAQ,CAAC,EAAE,CAAC,EAAE,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;IAC3C,OAAO,IAAI,UAAU,CAAC;IACtB,gCAAgC;IAChC,UAAU,IAAI,UAAU,CAAC;IACzB,KAAK,IAAI,MAAM,CAAC;IAChB,UAAU,CAAC,UAAU,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,OAAO,GAAG,YAAY,CAAC;IAChE,iDAAiD;IACjD,cAAc,CAAC,UAAU,EAAE,MAAM,GAAG,IAAI,CAAC;CAC1C;AACD,uEAAuE;AACvE,MAAM,WAAW,mBAAoB,SAAQ,gBAAgB,CAAC,YAAY,CAAC;IACzE,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG,YAAY,CAAC;IAC/D,EAAE,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;IACnB,EAAE,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;IACnB,UAAU,CAAC,CAAC,EAAE,WAAW,CAAC,MAAM,CAAC,GAAG,YAAY,CAAC;IACjD,SAAS,CAAC,KAAK,EAAE,UAAU,EAAE,MAAM,CAAC,EAAE,OAAO,GAAG,YAAY,CAAC;IAC7D,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,EAAE,OAAO,GAAG,YAAY,CAAC;IAClD,GAAG,CAAC,MAAM,EAAE,YAAY,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,YAAY,CAAC;CAC9D;AAED;;;;;;;;;;GAUG;AACH,MAAM,MAAM,WAAW,GAAG,QAAQ,CAAC;IACjC,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;IACV,EAAE,EAAE,MAAM,CAAC;IACX,EAAE,EAAE,MAAM,CAAC;CACZ,CAAC,CAAC;AAEH;;;;;;GAMG;AACH,MAAM,MAAM,gBAAgB,GAAG,OAAO,CAAC;IACrC,EAAE,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;IACnB,EAAE,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;IACnB,OAAO,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,KAAK;QAAE,OAAO,EAAE,OAAO,CAAC;QAAC,KAAK,EAAE,MAAM,CAAA;KAAE,CAAC;CACxE,CAAC,CAAC;AAEH;;;;;;;;;GASG;AACH,MAAM,MAAM,SAAS,GAAG;IACtB,IAAI,EAAE,KAAK,CAAC;IACZ,iBAAiB,CAAC,EAAE,CAAC,KAAK,EAAE,UAAU,KAAK,UAAU,CAAC;IACtD,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,UAAU,EAAE,GAAG,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,KAAK,UAAU,CAAC;IAC5E,UAAU,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,WAAW,CAAC,MAAM,CAAC,CAAC;IACvD,OAAO,CAAC,EAAE,KAAK,CAAC;IAChB,WAAW,CAAC,EAAE,CAAC,WAAW,CAAC,EAAE,MAAM,KAAK,UAAU,CAAC;CACpD,CAAC;AAEF;;;;GAIG;AACH,MAAM,WAAW,KAAK;IACpB,YAAY,EAAE,CAAC,UAAU,EAAE,GAAG,KAAK,UAAU,CAAC;IAC9C,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE,UAAU,EAAE,GAAG,EAAE,OAAO,CAAC,EAAE;QAAE,OAAO,CAAC,EAAE,GAAG,CAAA;KAAE,KAAK,UAAU,CAAC;IACjF,MAAM,EAAE,CACN,GAAG,EAAE,GAAG,EACR,OAAO,EAAE,GAAG,EACZ,SAAS,EAAE,GAAG,EACd,OAAO,CAAC,EAAE;QAAE,OAAO,CAAC,EAAE,GAAG,CAAC;QAAC,MAAM,EAAE,OAAO,CAAA;KAAE,KACzC,OAAO,CAAC;IACb,KAAK,EAAE,mBAAmB,CAAC;IAC3B,KAAK,EAAE;QACL,gBAAgB,EAAE,MAAM,UAAU,CAAC;QACnC,oBAAoB,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK;YAClC,IAAI,EAAE,UAAU,CAAC;YACjB,MAAM,EAAE,UAAU,CAAC;YACnB,MAAM,EAAE,MAAM,CAAC;YACf,KAAK,EAAE,YAAY,CAAC;YACpB,UAAU,EAAE,UAAU,CAAC;SACxB,CAAC;QACF,2CAA2C;QAC3C,UAAU,EAAE,CAAC,UAAU,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,YAAY,KAAK,YAAY,CAAC;KACzE,CAAC;CACH;AAGD,MAAM,MAAM,OAAO,GAAG;IACpB,KAAK,EAAE,SAAS,CAAC;IACjB,YAAY,EAAE,CAAC,UAAU,EAAE,GAAG,KAAK,UAAU,CAAC;IAC9C,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE,UAAU,EAAE,GAAG,EAAE,OAAO,CAAC,EAAE;QAAE,OAAO,CAAC,EAAE,GAAG,CAAA;KAAE,KAAK,UAAU,CAAC;IACjF,MAAM,EAAE,CACN,GAAG,EAAE,GAAG,EACR,OAAO,EAAE,GAAG,EACZ,SAAS,EAAE,GAAG,EACd,OAAO,CAAC,EAAE;QAAE,OAAO,CAAC,EAAE,GAAG,CAAC;QAAC,MAAM,EAAE,OAAO,CAAA;KAAE,KACzC,OAAO,CAAC;IACb,KAAK,EAAE,mBAAmB,CAAC;IAC3B,8BAA8B;IAC9B,aAAa,EAAE,mBAAmB,CAAC;IACnC,KAAK,EAAE;QACL,gBAAgB,EAAE,MAAM,UAAU,CAAC;QACnC,oBAAoB,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK;YAClC,IAAI,EAAE,UAAU,CAAC;YACjB,MAAM,EAAE,UAAU,CAAC;YACnB,MAAM,EAAE,MAAM,CAAC;YACf,KAAK,EAAE,YAAY,CAAC;YACpB,UAAU,EAAE,UAAU,CAAC;SACxB,CAAC;QACF,UAAU,EAAE,CAAC,UAAU,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,YAAY,KAAK,YAAY,CAAC;KACzE,CAAC;CACH,CAAC;AAUF,wBAAgB,OAAO,CAAC,KAAK,EAAE,WAAW,EAAE,SAAS,GAAE,gBAAqB,GAAG,mBAAmB,CA0TjG;AAED;;GAEG;AACH,wBAAgB,KAAK,CAAC,KAAK,EAAE,mBAAmB,EAAE,SAAS,EAAE,SAAS,GAAG,KAAK,CA4I7E;AAED,MAAM,MAAM,UAAU,GAAG;IACvB,KAAK,EAAE,WAAW,CAAC;IACnB,SAAS,EAAE,gBAAgB,CAAC;IAC5B,SAAS,EAAE,SAAS,CAAC;CACtB,CAAC;AA6BF,wBAAgB,cAAc,CAAC,CAAC,EAAE,mBAAmB,GAAG,OAAO,CAK9D"}
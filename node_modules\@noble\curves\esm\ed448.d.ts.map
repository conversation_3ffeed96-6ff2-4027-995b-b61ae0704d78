{"version": 3, "file": "ed448.d.ts", "sourceRoot": "", "sources": ["../src/ed448.ts"], "names": [], "mappings": "AAgBA,OAAO,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,MAAM,qBAAqB,CAAC;AAE9D,OAAO,EACL,KAAK,OAAO,EAGZ,KAAK,mBAAmB,EACxB,KAAK,YAAY,EAElB,MAAM,uBAAuB,CAAC;AAC/B,OAAO,EAGL,KAAK,SAAS,EACd,KAAK,SAAS,EACd,KAAK,YAAY,EAClB,MAAM,6BAA6B,CAAC;AAErC,OAAO,EAAc,KAAK,OAAO,IAAI,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AAChF,OAAO,EAKL,KAAK,GAAG,EAET,MAAM,YAAY,CAAC;AA2CpB,eAAO,MAAM,IAAI,EAAE,mBAAyC,CAAC;AAsF7D;;;;;;;;;GASG;AACH,eAAO,MAAM,KAAK,EAAE,OAAmC,CAAC;AAExD,eAAO,MAAM,OAAO,EAAE,OAIf,CAAC;AAER;;;;GAIG;AACH,eAAO,MAAM,IAAI,EAAE,QAYf,CAAC;AAEL;;;;;;;GAOG;AACH,wBAAgB,sBAAsB,CAAC,UAAU,EAAE,MAAM,GAAG,UAAU,GAAG,UAAU,CAKlF;AAED,eAAO,MAAM,mBAAmB,EAAE,OAAO,sBAA+C,CAAC;AAgFzF,eAAO,MAAM,YAAY,EAAE,SAAS,CAAC,MAAM,CASpC,CAAC;AACR,eAAO,MAAM,WAAW,EAAE,SAAS,CAAC,MAAM,CAAsD,CAAC;AACjG,eAAO,MAAM,aAAa,EAAE,SAAS,CAAC,MAAM,CACb,CAAC;AA0BhC,KAAK,aAAa,GAAG,YAAY,CAAC;AAoClC;;;;;;GAMG;AACH,cAAM,QAAS,YAAW,KAAK,CAAC,QAAQ,CAAC;IACvC,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC;IACtB,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC;IACtB,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAgB;gBAGvB,EAAE,EAAE,aAAa;IAI7B,MAAM,CAAC,UAAU,CAAC,EAAE,EAAE,WAAW,CAAC,MAAM,CAAC,GAAG,QAAQ;IAIpD;;;;;;;OAOG;IACH,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,GAAG,QAAQ;IAStC,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,UAAU,GAAG,QAAQ;IAK7C;;;;OAIG;IACH,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,GAAG,QAAQ;IA8BlC,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,QAAQ;IAK3D;;;OAGG;IACH,OAAO,IAAI,UAAU;IAoBrB,gCAAgC;IAChC,UAAU,IAAI,UAAU;IAIxB,KAAK,IAAI,MAAM;IAIf,QAAQ,IAAI,MAAM;IAIlB;;;OAGG;IACH,MAAM,CAAC,KAAK,EAAE,QAAQ,GAAG,OAAO;IAShC,GAAG,CAAC,KAAK,EAAE,QAAQ,GAAG,QAAQ;IAK9B,QAAQ,CAAC,KAAK,EAAE,QAAQ,GAAG,QAAQ;IAKnC,QAAQ,CAAC,MAAM,EAAE,MAAM,GAAG,QAAQ;IAIlC,cAAc,CAAC,MAAM,EAAE,MAAM,GAAG,QAAQ;IAIxC,MAAM,IAAI,QAAQ;IAIlB,MAAM,IAAI,QAAQ;CAGnB;AAED;;;GAGG;AACH,eAAO,MAAM,UAAU,EAAE,OAAO,QAM5B,CAAC;AAEL;;;GAGG;AACH,eAAO,MAAM,cAAc,GAAI,KAAK,UAAU,EAAE,SAAS,YAAY,KAAG,QAMvE,CAAC;AACF,eAAO,MAAM,gBAAgB,EAAE,OAAO,cAA+B,CAAC"}